import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, forkJoin } from 'rxjs';
import { environment } from '../../../environments/environment';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { switchMap, catchError, map } from 'rxjs/operators';

export interface TestResultWithScore {
  id: string;
  testId: string;
  userId: string;
  candidateName: string;
  testName: string;
  submittedAt: Date;
  totalQuestions: number;
  correctAnswers: number;
  scorePercentage: number;
  // Nouvelles propriétés pour le filtrage
  location?: string;
  skills?: string[];
  experience?: string;
  profileImage?: string | null;
}

export interface TestResultDisplay {
  id: string;
  userId: string;
  job: string;
  name: string;
  date: string;
  timeLimit: string;
  timePassed: string;
  score: string;
  correctAnswers: number;
  totalQuestions: number;
  // Nouvelles propriétés pour le filtrage
  location: string;
  skills: string[];
  experience: string;
  profileImage: string;
  scorePercentage: number;
}

export interface FilterOptions {
  location: string;
  minScore: number;
  maxScore: number;
  skills: string[];
  experience: string;
}

export interface PaginationOptions {
  page: number;
  size: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNext: boolean;
  hasPrevious: boolean;
  isFirst: boolean;
  isLast: boolean;
}

export interface CandidateAction {
  candidateId: string;
  candidateName: string;
  candidateEmail: string;
  action: 'accept' | 'reject' | 'pending';
  testResultId: string;
  score: number;
  testName: string;
}

export interface InterviewSchedule {
  candidateId: string;
  candidateName: string;
  candidateEmail: string;
  interviewDate: Date;
  interviewTime: string;
  location: string;
  interviewType: 'presentiel' | 'visio' | 'telephonique';
  recruiterName: string;
  recruiterEmail: string;
  additionalNotes?: string;
}

export interface EmailTemplate {
  to: string;
  subject: string;
  htmlContent: string;
  textContent: string;
}

export interface SortOption {
  field: string;
  label: string;
  direction: 'asc' | 'desc';
}

export interface InterviewRequest {
  candidateId: string;
  candidateName: string;
  candidateEmail: string;
  testResultId: string;
  interviewDate: Date;
  interviewTime: string;
  location: string;
  interviewType: 'presentiel' | 'visio' | 'telephonique';
  message?: string;
  recruiterName: string;
  recruiterEmail: string;
  companyName: string;
}

export interface CandidateStatus {
  candidateId: string;
  status: 'pending' | 'accepted' | 'rejected' | 'interview_scheduled';
  updatedAt: Date;
  updatedBy: string;
}

export interface UserInfo {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  address?: string;
}

export interface UserProfile {
  userId: string;
  bio?: string;
  location?: string;
  skills?: string[];
  experience?: string;
  photoUrl?: string | null;
  educations?: any[];
  certifications?: any[];
  links?: any[];
}

@Injectable({
  providedIn: 'root'
})
export class TestResultService {
  private apiUrl = `${environment.apiUrl}/tests`;
  private forceRealData = true; // Forcer l'utilisation des vraies données

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) {}

  /**
   * Teste la connexion à l'API backend
   */
  testApiConnection(): Observable<any> {
    console.log('🔍 Test de connexion à l\'API backend...');
    console.log('🌐 URL testée:', `${environment.apiUrl}/tests`);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

        // Tester d'abord l'endpoint des tests
        return this.http.get(`${environment.apiUrl}/tests`, { headers }).pipe(
          map(response => {
            console.log('✅ Connexion API réussie!');
            console.log('📊 Réponse:', response);
            return { success: true, data: response };
          }),
          catchError(error => {
            console.error('❌ Échec de connexion à l\'API:');
            console.error('📄 Erreur:', error);
            return of({ success: false, error: error });
          })
        );
      }),
      catchError(error => {
        console.error('❌ Erreur d\'authentification lors du test:', error);
        return of({ success: false, error: error });
      })
    );
  }

  /**
   * Récupère les résultats de test sans authentification (pour debug)
   */
  getTestResultsWithoutAuth(): Observable<any> {
    console.log('🔍 Test de récupération des résultats sans authentification...');
    const fullUrl = `${this.apiUrl}/results`;
    console.log('📡 URL appelée:', fullUrl);

    return this.http.get(fullUrl).pipe(
      map(results => {
        console.log('✅ Résultats récupérés sans auth:', results);
        return results;
      }),
      catchError(error => {
        console.error('❌ Erreur sans auth:', error);
        return of({ error: error, message: 'Échec sans authentification' });
      })
    );
  }

  /**
   * Récupère tous les résultats de test avec scores calculés (sans pagination)
   */
  getAllTestResults(): Observable<TestResultWithScore[]> {
    console.log('🔍 Récupération de tous les résultats de test depuis l\'API...');
    console.log('🌐 URL de l\'API configurée:', environment.apiUrl);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        console.log('🔑 Token d\'authentification récupéré:', token ? 'Présent' : 'Absent');

        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        const fullUrl = `${this.apiUrl}/results`;
        console.log('📡 Appel API vers:', fullUrl);

        return this.http.get<TestResultWithScore[]>(fullUrl, { headers }).pipe(
          map(results => {
            console.log('✅ Résultats récupérés depuis l\'API:', results);
            console.log(`📊 Nombre de résultats: ${results.length}`);

            // Afficher un échantillon des données
            if (results.length > 0) {
              console.log('📋 Premier résultat:', results[0]);
            }

            return results;
          }),
          catchError(error => {
            console.error('❌ Erreur lors de la récupération des résultats depuis l\'API:');
            console.error('📄 Détails de l\'erreur:', error);
            console.error('🔗 URL appelée:', fullUrl);
            console.error('📊 Status:', error.status);
            console.error('💬 Message:', error.message);

            if (error.status === 0) {
              console.error('🚫 Erreur de connexion - Vérifiez que le serveur backend est démarré sur le port 8080');
            } else if (error.status === 401) {
              console.error('🔐 Erreur d\'authentification - Token invalide ou expiré');
            } else if (error.status === 404) {
              console.error('🔍 Endpoint non trouvé - Vérifiez l\'URL de l\'API');
            }

            // Si l'API échoue, utiliser des données simulées comme fallback
            console.log('🔄 Utilisation des données simulées comme fallback');
            return of(this.getMockTestResults());
          })
        );
      }),
      catchError(error => {
        console.error('❌ Erreur d\'authentification:', error);
        // En cas d'erreur d'authentification, utiliser des données simulées
        console.log('🔄 Utilisation des données simulées (erreur auth)');
        return of(this.getMockTestResults());
      })
    );
  }

  /**
   * Récupère les résultats de test avec pagination
   */
  getTestResultsPaginated(
    paginationOptions: PaginationOptions,
    filters?: Partial<FilterOptions>
  ): Observable<PaginatedResponse<TestResultWithScore>> {
    console.log('🔍 Récupération paginée des résultats de test:', paginationOptions);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

        // Construire les paramètres de requête
        let params = new URLSearchParams();
        params.append('page', paginationOptions.page.toString());
        params.append('size', paginationOptions.size.toString());

        if (paginationOptions.sortBy) {
          params.append('sortBy', paginationOptions.sortBy);
        }
        if (paginationOptions.sortOrder) {
          params.append('sortOrder', paginationOptions.sortOrder);
        }

        // Ajouter les filtres
        if (filters) {
          if (filters.location && filters.location !== 'all') {
            params.append('location', filters.location);
          }
          if (filters.minScore !== undefined) {
            params.append('minScore', filters.minScore.toString());
          }
          if (filters.maxScore !== undefined) {
            params.append('maxScore', filters.maxScore.toString());
          }
          if (filters.experience && filters.experience !== 'all') {
            params.append('experience', filters.experience);
          }
          if (filters.skills && filters.skills.length > 0) {
            params.append('skills', filters.skills.join(','));
          }
        }

        const url = `${this.apiUrl}/results/paginated?${params.toString()}`;
        console.log('📡 Appel API paginé vers:', url);

        return this.http.get<PaginatedResponse<TestResultWithScore>>(url, { headers }).pipe(
          catchError(error => {
            console.error('❌ Erreur lors de la récupération paginée:', error);

            // Fallback avec pagination simulée
            return this.getPaginatedMockData(paginationOptions, filters);
          })
        );
      }),
      catchError(error => {
        console.error('❌ Erreur d\'authentification pour pagination:', error);
        return this.getPaginatedMockData(paginationOptions, filters);
      })
    );
  }

  /**
   * Récupère un résultat de test spécifique
   */
  getTestResult(resultId: string): Observable<TestResultWithScore> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<TestResultWithScore>(`${this.apiUrl}/results/${resultId}`, { headers });
      })
    );
  }

  /**
   * Récupère les informations utilisateur depuis l'API
   */
  getUserInfo(userId: string): Observable<UserInfo> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<UserInfo>(`${environment.apiUrl}/users/${userId}`, { headers });
      }),
      catchError(error => {
        console.error('❌ Erreur lors de la récupération des infos utilisateur:', error);
        return of({
          id: userId,
          firstName: 'Utilisateur',
          lastName: 'Inconnu',
          email: '<EMAIL>'
        } as UserInfo);
      })
    );
  }

  /**
   * Récupère le profil utilisateur depuis l'API
   */
  getUserProfile(userId: string): Observable<UserProfile> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<UserProfile>(`${environment.apiUrl}/user-profiles/${userId}`, { headers });
      }),
      catchError(error => {
        console.error('❌ Erreur lors de la récupération du profil utilisateur:', error);
        return of({
          userId: userId,
          bio: 'Profil non disponible',
          location: 'Non spécifié',
          skills: [],
          experience: 'Non spécifié',
          photoUrl: null,
          educations: [],
          certifications: [],
          links: []
        } as UserProfile);
      })
    );
  }

  /**
   * Enrichit les données de test avec des informations utilisateur réelles
   */
  private enrichTestResultData(results: TestResultWithScore[]): Observable<TestResultWithScore[]> {
    console.log('🔄 Enrichissement des données avec les profils utilisateur...');

    // Créer un tableau d'observables pour récupérer les infos de chaque utilisateur
    const enrichedResults$ = results.map(result => {
      return forkJoin({
        userInfo: this.getUserInfo(result.userId),
        userProfile: this.getUserProfile(result.userId)
      }).pipe(
        map(({ userInfo, userProfile }) => ({
          ...result,
          candidateName: result.candidateName || `${userInfo.firstName} ${userInfo.lastName}`,
          location: userProfile.location || userInfo.address || 'Non spécifié',
          skills: userProfile.skills || [],
          experience: userProfile.experience || 'Non spécifié',
          profileImage: userProfile.photoUrl || this.generateProfileImageUrl(`${userInfo.firstName} ${userInfo.lastName}`, result.userId)
        })),
        catchError(error => {
          console.error(`❌ Erreur lors de l'enrichissement pour l'utilisateur ${result.userId}:`, error);
          return of({
            ...result,
            location: result.location || 'Non spécifié',
            skills: result.skills || [],
            experience: result.experience || 'Non spécifié',
            profileImage: result.profileImage || this.generateProfileImageUrl(result.candidateName, result.userId)
          });
        })
      );
    });

    // Combiner tous les observables
    return forkJoin(enrichedResults$);
  }

  /**
   * Génère une URL d'image de profil basée sur le nom du candidat
   */
  private generateProfileImageUrl(candidateName: string, userId: string): string {
    if (!candidateName) {
      return `https://ui-avatars.com/api/?name=User&background=001040&color=fff&size=64`;
    }

    // Nettoyer le nom pour l'URL
    const cleanName = encodeURIComponent(candidateName.replace(/\s+/g, '+'));

    // Utiliser UI Avatars avec les couleurs de votre charte graphique
    return `https://ui-avatars.com/api/?name=${cleanName}&background=001040&color=fff&size=64&font-size=0.33`;
  }

  /**
   * Transforme les données du backend en format d'affichage avec enrichissement dynamique
   */
  transformToDisplayFormat(results: TestResultWithScore[]): Observable<TestResultDisplay[]> {
    console.log('🔄 Transformation des données de test:', results.length, 'résultats');

    // Enrichir les données avec des informations utilisateur réelles
    return this.enrichTestResultData(results).pipe(
      map(enrichedResults => {
        return enrichedResults.map((result, index) => {
          const displayResult: TestResultDisplay = {
            id: result.id,
            userId: result.userId || `user-${index + 1}`,
            job: result.testName || 'Test technique',
            name: result.candidateName || 'Candidat inconnu',
            date: this.formatDate(result.submittedAt),
            timeLimit: '1h', // Valeur par défaut, peut être récupérée du test si nécessaire
            timePassed: 'N/A', // Peut être calculé si on stocke le temps de début
            score: `${Math.round(result.scorePercentage || 0)}%`,
            correctAnswers: result.correctAnswers || 0,
            totalQuestions: result.totalQuestions || 0,
            location: result.location || 'Non spécifié',
            skills: result.skills || ['Compétences non spécifiées'],
            experience: result.experience || 'Non spécifié',
            profileImage: result.profileImage || this.generateProfileImageUrl(result.candidateName, result.userId),
            scorePercentage: result.scorePercentage || 0
          };

          console.log(`✅ Résultat transformé ${index + 1}:`, {
            name: displayResult.name,
            score: displayResult.score,
            location: displayResult.location,
            skills: displayResult.skills.length
          });

          return displayResult;
        });
      }),
      catchError(error => {
        console.error('❌ Erreur lors de la transformation des données:', error);
        // Fallback vers transformation simple sans enrichissement
        return of(this.transformToDisplayFormatSimple(results));
      })
    );
  }

  /**
   * Transformation simple sans enrichissement (fallback)
   */
  private transformToDisplayFormatSimple(results: TestResultWithScore[]): TestResultDisplay[] {
    return results.map((result, index) => ({
      id: result.id,
      userId: result.userId || `user-${index + 1}`,
      job: result.testName || 'Test technique',
      name: result.candidateName || 'Candidat inconnu',
      date: this.formatDate(result.submittedAt),
      timeLimit: '1h',
      timePassed: 'N/A',
      score: `${Math.round(result.scorePercentage || 0)}%`,
      correctAnswers: result.correctAnswers || 0,
      totalQuestions: result.totalQuestions || 0,
      location: 'Non spécifié',
      skills: ['Compétences non spécifiées'],
      experience: 'Non spécifié',
      profileImage: this.generateProfileImageUrl(result.candidateName, result.userId),
      scorePercentage: result.scorePercentage || 0
    }));
  }

  /**
   * Formate une date en format français
   */
  private formatDate(date: Date): string {
    if (!date) return 'N/A';

    const d = new Date(date);
    const day = d.getDate().toString().padStart(2, '0');
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const year = d.getFullYear();

    return `${day}/${month}/${year}`;
  }

  /**
   * Calcule la couleur du score selon le pourcentage
   */
  getScoreColor(scorePercentage: number): string {
    if (scorePercentage >= 80) return '#22C55E'; // Vert
    if (scorePercentage >= 60) return '#F59E0B'; // Orange
    return '#EF4444'; // Rouge
  }

  /**
   * Retourne le statut textuel du score
   */
  getScoreStatus(scorePercentage: number): string {
    if (scorePercentage >= 80) return 'Excellent';
    if (scorePercentage >= 60) return 'Bien';
    if (scorePercentage >= 40) return 'Moyen';
    return 'Faible';
  }

  /**
   * Filtre les résultats selon les critères spécifiés
   */
  filterResults(results: TestResultDisplay[], filters: Partial<FilterOptions>): TestResultDisplay[] {
    return results.filter(result => {
      // Filtre par lieu
      if (filters.location && filters.location !== 'all' &&
          !result.location.toLowerCase().includes(filters.location.toLowerCase())) {
        return false;
      }

      // Filtre par score minimum
      if (filters.minScore !== undefined && result.scorePercentage < filters.minScore) {
        return false;
      }

      // Filtre par score maximum
      if (filters.maxScore !== undefined && result.scorePercentage > filters.maxScore) {
        return false;
      }

      // Filtre par compétences
      if (filters.skills && filters.skills.length > 0) {
        const hasMatchingSkill = filters.skills.some(skill =>
          result.skills.some(resultSkill =>
            resultSkill.toLowerCase().includes(skill.toLowerCase())
          )
        );
        if (!hasMatchingSkill) {
          return false;
        }
      }

      // Filtre par expérience
      if (filters.experience && filters.experience !== 'all' &&
          result.experience.toLowerCase() !== filters.experience.toLowerCase()) {
        return false;
      }

      return true;
    });
  }

  /**
   * Trie les résultats selon le critère spécifié
   */
  sortResults(results: TestResultDisplay[], sortBy: string, sortOrder: 'asc' | 'desc' = 'desc'): TestResultDisplay[] {
    return [...results].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'score':
          comparison = a.scorePercentage - b.scorePercentage;
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'date':
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
          break;
        case 'location':
          comparison = a.location.localeCompare(b.location);
          break;
        case 'experience':
          const expOrder = { 'Junior': 1, 'Intermédiaire': 2, 'Senior': 3, 'Expert': 4 };
          comparison = (expOrder[a.experience as keyof typeof expOrder] || 0) -
                      (expOrder[b.experience as keyof typeof expOrder] || 0);
          break;
        default:
          return 0;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }

  /**
   * Récupère toutes les locations uniques
   */
  getUniqueLocations(results: TestResultDisplay[]): string[] {
    const locations = results.map(result => result.location).filter(Boolean);
    return [...new Set(locations)].sort();
  }

  /**
   * Récupère toutes les compétences uniques
   */
  getUniqueSkills(results: TestResultDisplay[]): string[] {
    const allSkills = results.flatMap(result => result.skills);
    return [...new Set(allSkills)].sort();
  }

  /**
   * Récupère tous les niveaux d'expérience uniques
   */
  getUniqueExperiences(results: TestResultDisplay[]): string[] {
    const experiences = results.map(result => result.experience).filter(Boolean);
    return [...new Set(experiences)].sort();
  }

  /**
   * Accepte un candidat et programme un entretien
   */
  acceptCandidate(candidateAction: CandidateAction, interviewRequest: InterviewRequest): Observable<any> {
    console.log('✅ Acceptation du candidat:', candidateAction.candidateName);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

        // Mettre à jour le statut du candidat
        const statusUpdate = {
          candidateId: candidateAction.candidateId,
          status: 'accepted',
          testResultId: candidateAction.testResultId
        };

        // Appel API pour accepter le candidat
        return this.http.post(`${environment.apiUrl}/candidates/accept`, statusUpdate, { headers }).pipe(
          switchMap(() => {
            // Envoyer l'email d'invitation à l'entretien
            return this.sendInterviewInvitation(interviewRequest);
          }),
          catchError(error => {
            console.error('❌ Erreur lors de l\'acceptation du candidat:', error);
            // Fallback : envoyer quand même l'email
            return this.sendInterviewInvitation(interviewRequest);
          })
        );
      })
    );
  }

  /**
   * Rejette un candidat
   */
  rejectCandidate(candidateAction: CandidateAction): Observable<any> {
    console.log('❌ Rejet du candidat:', candidateAction.candidateName);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

        const statusUpdate = {
          candidateId: candidateAction.candidateId,
          status: 'rejected',
          testResultId: candidateAction.testResultId
        };

        return this.http.post(`${environment.apiUrl}/candidates/reject`, statusUpdate, { headers }).pipe(
          catchError(error => {
            console.error('❌ Erreur lors du rejet du candidat:', error);
            return of({ success: false, error: error.message });
          })
        );
      })
    );
  }

  /**
   * Envoie une invitation à l'entretien par email
   */
  sendInterviewInvitation(interviewRequest: InterviewRequest): Observable<any> {
    console.log('📧 Envoi de l\'invitation à l\'entretien pour:', interviewRequest.candidateName);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

        // Générer le contenu de l'email
        const emailContent = this.generateInterviewEmailContent(interviewRequest);

        const emailData = {
          to: interviewRequest.candidateEmail,
          subject: `Invitation à un entretien - ${interviewRequest.companyName}`,
          htmlContent: emailContent.html,
          textContent: emailContent.text,
          interviewDetails: interviewRequest
        };

        return this.http.post(`${environment.apiUrl}/emails/send-interview-invitation`, emailData, { headers }).pipe(
          map(response => {
            console.log('✅ Email d\'invitation envoyé avec succès');
            return { success: true, response };
          }),
          catchError(error => {
            console.error('❌ Erreur lors de l\'envoi de l\'email:', error);
            return of({ success: false, error: error.message });
          })
        );
      })
    );
  }

  /**
   * Génère le contenu de l'email d'invitation
   */
  private generateInterviewEmailContent(interviewRequest: InterviewRequest): { html: string, text: string } {
    const dateFormatted = this.formatDate(interviewRequest.interviewDate);
    const locationText = interviewRequest.interviewType === 'presentiel'
      ? `📍 Lieu : ${interviewRequest.location}`
      : interviewRequest.interviewType === 'visio'
      ? `💻 Entretien en visioconférence (lien à suivre)`
      : `📞 Entretien téléphonique`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #001040FF 0%, #001660FF 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
          .interview-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #001040FF; }
          .button { display: inline-block; background: #001040FF; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Félicitations ${interviewRequest.candidateName} !</h1>
            <p>Votre candidature a été retenue</p>
          </div>

          <div class="content">
            <p>Bonjour <strong>${interviewRequest.candidateName}</strong>,</p>

            <p>Nous avons le plaisir de vous informer que votre candidature pour le poste a retenu notre attention. Vos résultats au test technique sont excellents !</p>

            <div class="interview-details">
              <h3>📅 Détails de l'entretien</h3>
              <p><strong>Date :</strong> ${dateFormatted}</p>
              <p><strong>Heure :</strong> ${interviewRequest.interviewTime}</p>
              <p><strong>Type :</strong> ${this.getInterviewTypeLabel(interviewRequest.interviewType)}</p>
              <p>${locationText}</p>
              <p><strong>Avec :</strong> ${interviewRequest.recruiterName}</p>
              ${interviewRequest.message ? `<p><strong>Message :</strong> ${interviewRequest.message}</p>` : ''}
            </div>

            <p>Merci de confirmer votre présence en répondant à cet email.</p>

            <p>Nous avons hâte de vous rencontrer !</p>

            <div class="footer">
              <p>Cordialement,<br>
              <strong>${interviewRequest.recruiterName}</strong><br>
              ${interviewRequest.companyName}<br>
              📧 ${interviewRequest.recruiterEmail}</p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
Félicitations ${interviewRequest.candidateName} !

Votre candidature a été retenue.

Bonjour ${interviewRequest.candidateName},

Nous avons le plaisir de vous informer que votre candidature pour le poste a retenu notre attention. Vos résultats au test technique sont excellents !

DÉTAILS DE L'ENTRETIEN :
- Date : ${dateFormatted}
- Heure : ${interviewRequest.interviewTime}
- Type : ${this.getInterviewTypeLabel(interviewRequest.interviewType)}
- ${locationText}
- Avec : ${interviewRequest.recruiterName}
${interviewRequest.message ? `- Message : ${interviewRequest.message}` : ''}

Merci de confirmer votre présence en répondant à cet email.

Nous avons hâte de vous rencontrer !

Cordialement,
${interviewRequest.recruiterName}
${interviewRequest.companyName}
${interviewRequest.recruiterEmail}
    `;

    return { html, text };
  }

  /**
   * Retourne le label du type d'entretien
   */
  private getInterviewTypeLabel(type: string): string {
    switch (type) {
      case 'presentiel': return 'Entretien en présentiel';
      case 'visio': return 'Entretien en visioconférence';
      case 'telephonique': return 'Entretien téléphonique';
      default: return 'Entretien';
    }
  }

  /**
   * Récupère les options de tri disponibles
   */
  getSortOptions(): SortOption[] {
    return [
      { field: 'score', label: 'Score (décroissant)', direction: 'desc' },
      { field: 'score', label: 'Score (croissant)', direction: 'asc' },
      { field: 'name', label: 'Nom (A-Z)', direction: 'asc' },
      { field: 'name', label: 'Nom (Z-A)', direction: 'desc' },
      { field: 'date', label: 'Date (récent)', direction: 'desc' },
      { field: 'date', label: 'Date (ancien)', direction: 'asc' },
      { field: 'location', label: 'Lieu (A-Z)', direction: 'asc' },
      { field: 'experience', label: 'Expérience (croissant)', direction: 'asc' },
      { field: 'experience', label: 'Expérience (décroissant)', direction: 'desc' }
    ];
  }

  /**
   * Pagination simulée pour le fallback
   */
  private getPaginatedMockData(
    paginationOptions: PaginationOptions,
    filters?: Partial<FilterOptions>
  ): Observable<PaginatedResponse<TestResultWithScore>> {
    console.log('⚠️ Utilisation de la pagination simulée - API non disponible');

    // Générer des données de test plus réalistes pour la pagination
    const mockData = this.generateExtendedMockData();

    // Appliquer les filtres
    let filteredData = mockData;
    if (filters) {
      filteredData = this.filterMockResults(mockData, filters);
    }

    // Appliquer le tri
    if (paginationOptions.sortBy) {
      filteredData = this.sortMockResults(filteredData, paginationOptions.sortBy, paginationOptions.sortOrder || 'desc');
    }

    // Calculer la pagination
    const totalElements = filteredData.length;
    const totalPages = Math.ceil(totalElements / paginationOptions.size);
    const startIndex = paginationOptions.page * paginationOptions.size;
    const endIndex = startIndex + paginationOptions.size;
    const content = filteredData.slice(startIndex, endIndex);

    const paginatedResponse: PaginatedResponse<TestResultWithScore> = {
      content,
      totalElements,
      totalPages,
      currentPage: paginationOptions.page,
      pageSize: paginationOptions.size,
      hasNext: paginationOptions.page < totalPages - 1,
      hasPrevious: paginationOptions.page > 0,
      isFirst: paginationOptions.page === 0,
      isLast: paginationOptions.page === totalPages - 1
    };

    return of(paginatedResponse);
  }

  /**
   * Génère des données de test étendues pour la pagination
   */
  private generateExtendedMockData(): TestResultWithScore[] {
    const names = [
      'Marie Dubois', 'Pierre Martin', 'Sophie Leroy', 'Thomas Moreau', 'Emma Rousseau',
      'Lucas Bernard', 'Camille Petit', 'Antoine Durand', 'Léa Moreau', 'Hugo Lefebvre',
      'Chloé Simon', 'Maxime Michel', 'Manon Garcia', 'Nathan Rodriguez', 'Inès Martinez',
      'Théo David', 'Jade Roux', 'Louis Bertrand', 'Lola Fournier', 'Gabriel Morel',
      'Zoé Girard', 'Arthur André', 'Lina Leroy', 'Raphaël Mercier', 'Mila Blanc'
    ];

    const testTypes = [
      'Test Angular Developer', 'Test React Developer', 'Test Vue.js Developer',
      'Test Node.js Backend', 'Test Python Developer', 'Test Java Spring Boot',
      'Test DevOps Engineer', 'Test Full Stack', 'Test Frontend', 'Test Backend'
    ];

    const locations = [
      'Paris, France', 'Lyon, France', 'Marseille, France', 'Toulouse, France',
      'Nice, France', 'Nantes, France', 'Strasbourg, France', 'Montpellier, France',
      'Bordeaux, France', 'Lille, France', 'Rennes, France', 'Reims, France'
    ];

    const experiences = ['Junior', 'Intermédiaire', 'Senior', 'Expert'];

    const skillSets = [
      ['JavaScript', 'TypeScript', 'Angular', 'RxJS'],
      ['React', 'JavaScript', 'Redux', 'Node.js'],
      ['Vue.js', 'JavaScript', 'Vuex', 'Nuxt.js'],
      ['Python', 'Django', 'PostgreSQL', 'Redis'],
      ['Java', 'Spring Boot', 'MySQL', 'Docker'],
      ['Node.js', 'Express', 'MongoDB', 'GraphQL'],
      ['AWS', 'Kubernetes', 'Jenkins', 'Terraform'],
      ['C#', '.NET', 'SQL Server', 'Azure'],
      ['PHP', 'Laravel', 'MySQL', 'Redis'],
      ['Go', 'Gin', 'PostgreSQL', 'Docker']
    ];

    return names.map((name, index) => ({
      id: `mock-${index + 1}`,
      testId: `test-${index + 1}`,
      userId: `user-${String(index + 1).padStart(3, '0')}`,
      candidateName: name,
      testName: testTypes[index % testTypes.length],
      submittedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // 30 derniers jours
      totalQuestions: Math.floor(Math.random() * 20) + 10, // 10-30 questions
      correctAnswers: Math.floor(Math.random() * 25) + 5, // 5-30 bonnes réponses
      scorePercentage: Math.floor(Math.random() * 60) + 40, // 40-100%
      location: locations[index % locations.length],
      skills: skillSets[index % skillSets.length],
      experience: experiences[index % experiences.length],
      profileImage: `https://ui-avatars.com/api/?name=${encodeURIComponent(name.replace(/\s+/g, '+'))}&background=001040&color=fff&size=64`
    }));
  }

  /**
   * Filtre les données simulées
   */
  private filterMockResults(results: TestResultWithScore[], filters: Partial<FilterOptions>): TestResultWithScore[] {
    return results.filter(result => {
      if (filters.location && filters.location !== 'all' &&
          !result.location?.toLowerCase().includes(filters.location.toLowerCase())) {
        return false;
      }

      if (filters.minScore !== undefined && result.scorePercentage < filters.minScore) {
        return false;
      }

      if (filters.maxScore !== undefined && result.scorePercentage > filters.maxScore) {
        return false;
      }

      if (filters.experience && filters.experience !== 'all' &&
          result.experience?.toLowerCase() !== filters.experience.toLowerCase()) {
        return false;
      }

      if (filters.skills && filters.skills.length > 0) {
        const hasMatchingSkill = filters.skills.some(skill =>
          result.skills?.some(resultSkill =>
            resultSkill.toLowerCase().includes(skill.toLowerCase())
          )
        );
        if (!hasMatchingSkill) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Trie les données simulées
   */
  private sortMockResults(results: TestResultWithScore[], sortBy: string, sortOrder: 'asc' | 'desc'): TestResultWithScore[] {
    return [...results].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'score':
          comparison = a.scorePercentage - b.scorePercentage;
          break;
        case 'name':
          comparison = a.candidateName.localeCompare(b.candidateName);
          break;
        case 'date':
          comparison = new Date(a.submittedAt).getTime() - new Date(b.submittedAt).getTime();
          break;
        case 'location':
          comparison = (a.location || '').localeCompare(b.location || '');
          break;
        default:
          return 0;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }

  /**
   * Planifie un entretien et envoie un email au candidat
   */
  scheduleInterview(interviewRequest: InterviewRequest): Observable<any> {
    console.log('📅 Planification d\'entretien pour:', interviewRequest.candidateName);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.post(`${this.apiUrl}/interviews/schedule`, interviewRequest, { headers });
      }),
      catchError(error => {
        console.error('❌ Erreur lors de la planification d\'entretien:', error);
        // Simulation d'envoi d'email pour la démonstration
        return this.simulateEmailSending(interviewRequest);
      })
    );
  }

  /**
   * Met à jour le statut d'un candidat
   */
  updateCandidateStatus(candidateId: string, status: CandidateStatus['status']): Observable<any> {
    console.log('🔄 Mise à jour du statut du candidat:', candidateId, 'vers', status);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        const statusUpdate = {
          candidateId,
          status,
          updatedAt: new Date(),
          updatedBy: 'current-user' // À remplacer par l'utilisateur connecté
        };
        return this.http.put(`${this.apiUrl}/candidates/${candidateId}/status`, statusUpdate, { headers });
      }),
      catchError(error => {
        console.error('❌ Erreur lors de la mise à jour du statut:', error);
        return of({ success: true, message: 'Statut mis à jour (mode simulation)' });
      })
    );
  }

  /**
   * Simulation d'envoi d'email (fallback)
   */
  private simulateEmailSending(interviewRequest: InterviewRequest): Observable<any> {
    console.log('📧 Simulation d\'envoi d\'email d\'entretien...');

    // Simuler un délai d'envoi avec Observable
    return new Observable(observer => {
      setTimeout(() => {
        console.log('✅ Email d\'entretien envoyé (simulation) à:', interviewRequest.candidateEmail);
        console.log('📋 Détails de l\'entretien:', {
          date: interviewRequest.interviewDate,
          time: interviewRequest.interviewTime,
          location: interviewRequest.location,
          type: interviewRequest.interviewType
        });

        const result = {
          success: true,
          message: 'Email d\'entretien envoyé avec succès',
          emailSent: true,
          interviewId: `interview-${Date.now()}`
        };

        observer.next(result);
        observer.complete();
      }, 1500);
    });
  }

  /**
   * Données de fallback minimales en cas d'échec total de l'API
   */
  private getMockTestResults(): TestResultWithScore[] {
    console.log('⚠️ Utilisation des données de fallback - API non disponible');
    return [
      {
        id: 'fallback-1',
        testId: 'test-fallback',
        userId: 'user-fallback',
        candidateName: 'Données non disponibles',
        testName: 'Test - API non disponible',
        submittedAt: new Date(),
        totalQuestions: 0,
        correctAnswers: 0,
        scorePercentage: 0,
        location: 'Non spécifié',
        skills: [],
        experience: 'Non spécifié',
        profileImage: null
      } as TestResultWithScore
    ];
  }
}
