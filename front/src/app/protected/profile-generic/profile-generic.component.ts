import {Component, OnInit} from '@angular/core';
import {WorkspaceService} from '../../services/workspace/workspace.service';
import {ProfileComponent} from '../profile/profile/profile.component';
import {RecruiterProfileComponent} from '../recruiter-profile/recruiter-profile.component';
import {NgIf} from '@angular/common';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-profile-generic',
  imports: [
    ProfileComponent,
    RecruiterProfileComponent,
    NgIf
  ],
  templateUrl: './profile-generic.component.html',
  standalone: true,
  styleUrl: './profile-generic.component.css'
})
export class ProfileGenericComponent implements OnInit{
  currentWorkspaceId: string = '';
  viewingUserId: string | null = null;

  constructor(
    private workspaceService: WorkspaceService,
    private route: ActivatedRoute
  ) {
  }

  ngOnInit(): void {
    // Récupérer l'ID de l'utilisateur depuis l'URL si présent
    this.route.params.subscribe(params => {
      this.viewingUserId = params['userId'] || null;
    });

    this.currentWorkspaceId = this.workspaceService.getCurrentWorkspaceId();
  }

}
