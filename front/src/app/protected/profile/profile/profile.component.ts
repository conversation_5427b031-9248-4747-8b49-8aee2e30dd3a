import { Component, OnInit, ViewChild, ElementRef, Input } from '@angular/core';
import { UserService } from '../../../services/user/user.service';
import { <PERSON><PERSON>ard, Mat<PERSON>ardContent, Mat<PERSON>ardHeader, MatCardModule, MatCardTitle } from '@angular/material/card';
import { <PERSON><PERSON>ridList, MatGridTile } from '@angular/material/grid-list';
import { CommonModule, NgForOf, NgIf } from '@angular/common';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatButton, MatButtonModule, MatIconButton } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AvatarService } from '../../../services/avatar/avatar.service';
import { AddSkillModalComponent } from '../../add-skill-modal/add-skill-modal.component';
import { MatDialog } from '@angular/material/dialog';
import { AddCvModalComponent } from '../../add-cv-modal/add-cv-modal.component';
import { AddLinkModalComponent } from '../../add-link-modal/add-link-modal.component';
import { AddCertificationModalComponent } from '../../add-certification-modal/add-certification-modal.component';
import { AddEducationModalComponent } from '../../add-education-modal/add-education-modal.component';
import { AddExperienceModalComponent } from '../../add-experience-modal/add-experience-modal.component';
import { EditProfileComponent } from '../../edit-profile/edit-profile.component';
import { CompleteProfileModalComponent } from '../complete-profile-modal/complete-profile-modal.component';
import { BioSectionComponent } from '../bio-section/bio-section.component';
import { ConfirmationService } from '../../../services/confirmation/confirmation.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
  standalone: true,
  imports: [
    MatCardContent,
    MatCard,
    MatGridTile,
    MatGridList,
    MatCardTitle,
    NgIf,
    NgForOf,
    MatCardHeader,
    MatIcon,
    MatIconButton,
    MatButton,
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    FormsModule,
    MatTooltipModule,
  ]
})
export class ProfileComponent implements OnInit {
  // Input pour afficher le profil d'un utilisateur spécifique
  @Input() viewingUserId: string | null = null;

  // Properties
  user: any = null;
  userProfile: any = null;
  workspace: any = null;
  isLoading = true;
  isViewingOtherProfile = false;
  avatarUrl: string | ArrayBuffer | null = null;
  isUploadingPhoto: boolean = false;
  photoUploadError: string | null = null;
  isUploadingCV: boolean = false;

  // Avatar par défaut en SVG (cercle orange avec silhouette blanche)
  defaultAvatarUrl: string = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI1MCIgZmlsbD0iI0ZGOTUwMCIvPjxwYXRoIGQ9Ik01MCwyMCBDNTguMjg0MjcxLDIwIDY1LDI2LjcxNTcyODggNjUsMzUgQzY1LDQzLjI4NDI3MTIgNTguMjg0MjcxLDUwIDUwLDUwIEM0MS43MTU3Mjg4LDUwIDM1LDQzLjI4NDI3MTIgMzUsMzUgQzM1LDI2LjcxNTcyODggNDEuNzE1NzI4OCwyMCA1MCwyMCBaIE01MCw1NSBDNjYuNTY4NTQyNSw1NSA4MCw2MS43MTU3Mjg4IDgwLDcwIEw4MCw4MCBMMjAsODAgTDIwLDcwIEMyMCw2MS43MTU3Mjg4IDMzLjQzMTQ1NzUsNTUgNTAsNTUgWiIgZmlsbD0iI0ZGRkZGRiIvPjwvc3ZnPg==';

  // Bio editing variables
  isEditingBio: boolean = false;
  tempBio: string = '';
  isSavingBio: boolean = false;

  // Toggle display variables
  showAllSkills: boolean = false;
  showAllEducations: boolean = false;
  showAllCertifications: boolean = false;
  showAllExperiences: boolean = false;
  showAllLinks: boolean = false;

  // Constructor
  constructor(
    private userService: UserService,
    private dialog: MatDialog,
    private avatarService: AvatarService,
    private confirmationService: ConfirmationService
  ) {
    console.log('Constructor called, showAllCertifications:', this.showAllCertifications);
  }

  /**
   * Gère les erreurs de chargement d'image et affiche l'avatar par défaut
   */
  onImageError(event: any): void {
    console.log('Erreur de chargement de l\'image, utilisation de l\'avatar par défaut');
    event.target.src = this.defaultAvatarUrl;
  }

  // Lifecycle Methods
  ngOnInit() {
    console.log('ngOnInit called');
    console.log('viewingUserId:', this.viewingUserId);

    // Déterminer si on affiche le profil d'un autre utilisateur
    this.isViewingOtherProfile = !!this.viewingUserId;

    if (this.isViewingOtherProfile) {
      // Charger le profil de l'utilisateur spécifié
      this.loadUserProfile(this.viewingUserId!);
    } else {
      // Charger le profil de l'utilisateur connecté (comportement normal)
      // Essayer de charger les données du localStorage d'abord pour un affichage rapide
      const savedData = localStorage.getItem('userData');
      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);
          this.user = parsedData.user || {};
          this.userProfile = parsedData.userProfile || {};
          this.workspace = parsedData.workspace || {};

          // S'assurer que toutes les listes sont initialisées
          this.userProfile.skills = this.userProfile.skills || [];
          this.userProfile.links = this.userProfile.links || [];
          this.userProfile.certifications = this.userProfile.certifications || [];
          this.userProfile.educations = this.userProfile.educations || [];
          this.userProfile.experiences = this.userProfile.experiences || [];

          console.log('Données initiales chargées depuis le localStorage');

          // Définir l'URL correcte pour les images en utilisant le service AvatarService
          if (this.userProfile) {
            // Utiliser l'avatar par défaut si aucune photo n'est définie
            this.avatarUrl = this.avatarService.getAvatarUrl(this.userProfile.photoUrl || null);
            console.log('Photo URL (localStorage):', this.avatarUrl);
          } else {
            // Si aucun profil n'est défini, utiliser l'avatar par défaut
            this.avatarUrl = this.avatarService.getAvatarUrl(null);
            console.log('Aucun profil, utilisation de l\'avatar par défaut');
          }
        } catch (error) {
          console.error('Erreur lors du parsing des données du localStorage:', error);
        }
      }

      // Charger les données depuis l'API ensuite
      this.loadUser();
    }
  }

  loadUser(): void {
    this.isLoading = true;
    console.log('loadUser started');

    // Stocker les valeurs actuelles des flags showAll pour les restaurer après le chargement
    const showAllStates = {
      skills: this.showAllSkills,
      educations: this.showAllEducations,
      certifications: this.showAllCertifications,
      experiences: this.showAllExperiences,
      links: this.showAllLinks
    };

    this.userService.getUserProfile().subscribe(
      (data: any) => {
        console.log('loadUser data received:', data);
        if (data) {
          // Sauvegarder les données utilisateur dans le localStorage pour la persistance
          localStorage.setItem('userData', JSON.stringify(data));

          this.user = data.user || {};
          this.userProfile = data.userProfile || {};
          this.workspace = data.workspace || {};

          // S'assurer que toutes les listes sont initialisées
          this.userProfile.skills = this.userProfile.skills || [];
          this.userProfile.links = this.userProfile.links || [];
          this.userProfile.certifications = this.userProfile.certifications || [];
          this.userProfile.educations = this.userProfile.educations || [];
          this.userProfile.experiences = this.userProfile.experiences || [];

          // Logs pour déboguer les données chargées
          console.log('Skills:', this.userProfile.skills);
          console.log('Links:', this.userProfile.links);
          console.log('Certifications:', this.userProfile.certifications);
          console.log('Educations:', this.userProfile.educations);
          console.log('Experiences:', this.userProfile.experiences);

          // Définir l'URL correcte pour les images en utilisant le service AvatarService
          // Utiliser l'avatar par défaut si aucune photo n'est définie
          this.avatarUrl = this.avatarService.getAvatarUrl(this.userProfile?.photoUrl || null);
          console.log('Photo URL:', this.avatarUrl);

          // Vérifier si le CV existe et ajouter la date d'upload si elle n'existe pas
          if (this.userProfile.cv && !this.userProfile.cv.uploadDate) {
            this.userProfile.cv.uploadDate = new Date().toISOString();
          }
        } else {
          // Essayer de récupérer les données du localStorage si l'API échoue
          const savedData = localStorage.getItem('userData');
          if (savedData) {
            const parsedData = JSON.parse(savedData);
            this.user = parsedData.user || {};
            this.userProfile = parsedData.userProfile || {};
            this.workspace = parsedData.workspace || {};
            console.log('Loaded data from localStorage:', parsedData);
          } else {
            // Initialiser avec des objets vides si aucune donnée n'est disponible
            this.user = {};
            this.userProfile = { skills: [], links: [], certifications: [], educations: [], experiences: [] };
            this.workspace = {};
          }
        }

        // Restaurer les états showAll précédents au lieu de les réinitialiser
        this.showAllSkills = showAllStates.skills;
        this.showAllEducations = showAllStates.educations;
        this.showAllCertifications = showAllStates.certifications;
        this.showAllExperiences = showAllStates.experiences;
        this.showAllLinks = showAllStates.links;

        console.log('États showAll restaurés:', {
          skills: this.showAllSkills,
          educations: this.showAllEducations,
          certifications: this.showAllCertifications,
          experiences: this.showAllExperiences,
          links: this.showAllLinks
        });

        this.isLoading = false;
        console.log('loadUser completed successfully');
      },
      (error) => {
        console.error('Erreur lors de la récupération du profil:', error);

        // Essayer de récupérer les données du localStorage en cas d'erreur
        const savedData = localStorage.getItem('userData');
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          this.user = parsedData.user || {};
          this.userProfile = parsedData.userProfile || {};
          this.workspace = parsedData.workspace || {};
          console.log('Loaded data from localStorage after error:', parsedData);
        } else {
          this.user = {};
          this.userProfile = { skills: [], links: [], certifications: [], educations: [], experiences: [] };
          this.workspace = {};
        }

        // Restaurer les états showAll précédents au lieu de les réinitialiser
        this.showAllSkills = showAllStates.skills;
        this.showAllEducations = showAllStates.educations;
        this.showAllCertifications = showAllStates.certifications;
        this.showAllExperiences = showAllStates.experiences;
        this.showAllLinks = showAllStates.links;

        console.log('États showAll restaurés après erreur:', {
          skills: this.showAllSkills,
          educations: this.showAllEducations,
          certifications: this.showAllCertifications,
          experiences: this.showAllExperiences,
          links: this.showAllLinks
        });

        this.isLoading = false;
        console.log('loadUser completed with error, using cached data if available');
      }
    );
  }

  /**
   * Charge le profil d'un utilisateur spécifique par son ID
   */
  loadUserProfile(userId: string): void {
    this.isLoading = true;
    console.log('loadUserProfile started for userId:', userId);

    // Pour l'instant, on simule les données d'un autre utilisateur
    // Dans une vraie application, vous feriez un appel API pour récupérer le profil de l'utilisateur
    this.userService.getUserProfileById(userId).subscribe(
      (data: any) => {
        console.log('loadUserProfile data received:', data);
        if (data) {
          this.user = data.user || {};
          this.userProfile = data.userProfile || {};
          this.workspace = data.workspace || {};

          // S'assurer que toutes les listes sont initialisées
          this.userProfile.skills = this.userProfile.skills || [];
          this.userProfile.links = this.userProfile.links || [];
          this.userProfile.certifications = this.userProfile.certifications || [];
          this.userProfile.educations = this.userProfile.educations || [];
          this.userProfile.experiences = this.userProfile.experiences || [];

          // Définir l'URL correcte pour les images
          this.avatarUrl = this.avatarService.getAvatarUrl(this.userProfile?.photoUrl || null);
          console.log('Photo URL for other user:', this.avatarUrl);
        } else {
          // Données par défaut si l'utilisateur n'est pas trouvé
          this.user = { firstName: 'Utilisateur', lastName: 'Inconnu' };
          this.userProfile = {
            skills: ['JavaScript', 'Angular', 'Node.js'],
            links: [],
            certifications: [],
            educations: [],
            experiences: [],
            bio: 'Profil non disponible',
            location: 'Non spécifié'
          };
          this.workspace = {};
          this.avatarUrl = this.avatarService.getAvatarUrl(null);
        }

        this.isLoading = false;
        console.log('loadUserProfile completed successfully');
      },
      (error) => {
        console.error('Erreur lors de la récupération du profil utilisateur:', error);

        // Données par défaut en cas d'erreur
        this.user = { firstName: 'Utilisateur', lastName: 'Inconnu' };
        this.userProfile = {
          skills: ['JavaScript', 'Angular', 'Node.js'],
          links: [],
          certifications: [],
          educations: [],
          experiences: [],
          bio: 'Profil non disponible',
          location: 'Non spécifié'
        };
        this.workspace = {};
        this.avatarUrl = this.avatarService.getAvatarUrl(null);

        this.isLoading = false;
        console.log('loadUserProfile completed with error, using default data');
      }
    );
  }

  // Toggle Display Methods
  private resetShowAll(): void {
    this.showAllSkills = false;
    this.showAllEducations = false;
    this.showAllCertifications = false;
    this.showAllExperiences = false;
    this.showAllLinks = false;
    this.isLoading = false;
    console.log('resetShowAll called, showAllCertifications:', this.showAllCertifications);
    // Ne pas appeler loadUser() ici pour éviter une boucle infinie
  }

  forceHideCertifications(): void {
    console.log('forceHideCertifications called, current showAllCertifications:', this.showAllCertifications);
    this.showAllCertifications = false;
    console.log('forceHideCertifications updated, new showAllCertifications:', this.showAllCertifications);
  }

  toggleSkills() {
    console.log('toggleSkills called, current showAllSkills:', this.showAllSkills);
    this.showAllSkills = !this.showAllSkills;
    console.log('toggleSkills updated, new showAllSkills:', this.showAllSkills);
  }

  toggleEducations() {
    console.log('toggleEducations called, current showAllEducations:', this.showAllEducations);
    this.showAllEducations = !this.showAllEducations;
    console.log('toggleEducations updated, new showAllEducations:', this.showAllEducations);
  }

  toggleCertifications() {
    console.log('toggleCertifications called, current showAllCertifications:', this.showAllCertifications);
    this.showAllCertifications = !this.showAllCertifications;
    console.log('toggleCertifications updated, new showAllCertifications:', this.showAllCertifications);
    console.log('userProfile.certifications:', this.userProfile.certifications);
  }

  toggleExperiences() {
    console.log('toggleExperiences called, current showAllExperiences:', this.showAllExperiences);
    this.showAllExperiences = !this.showAllExperiences;
    console.log('toggleExperiences updated, new showAllExperiences:', this.showAllExperiences);
  }

  toggleLinks() {
    console.log('toggleLinks called, current showAllLinks:', this.showAllLinks);
    this.showAllLinks = !this.showAllLinks;
    console.log('toggleLinks updated, new showAllLinks:', this.showAllLinks);
  }

  // CV Management Methods (voir implémentation complète plus bas)

  // Education Management Methods
  addEducation() {
    const dialogRef = this.dialog.open(AddEducationModalComponent, {
      width: '400px',
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        console.log('Formation ajoutée:', result);
        this.updateProfileWithEducation(result);
      }
    });
  }

  editEducation(education: any, index: number) {
    const dialogRef = this.dialog.open(AddEducationModalComponent, {
      width: '400px',
      data: education
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        console.log('Formation mise à jour:', result);
        this.userService.editEducation(result, index).subscribe(
          () => {
            console.log('Formation mise à jour avec succès');
            this.loadUser();
          },
          (error) => {
            console.error('Erreur lors de la mise à jour de la formation:', error);
            alert('Erreur lors de la mise à jour de la formation. Veuillez réessayer.');
          }
        );
      }
    });
  }

  deleteEducation(index: number) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette formation ?')) {
      this.userService.deleteEducation(index).subscribe(
        () => {
          console.log('Formation supprimée avec succès');
          this.loadUser();
        },
        (error) => {
          console.error('Erreur lors de la suppression de la formation:', error);
          alert('Erreur lors de la suppression de la formation. Veuillez réessayer.');
        }
      );
    }
  }

  private updateProfileWithEducation(education: any) {
    this.userService.addEducation(education).subscribe(
      (response: any) => {
        console.log('Profil mis à jour avec la formation:', response);

        // Mettre à jour le localStorage avec la nouvelle formation
        const savedData = localStorage.getItem('userData');
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          if (parsedData.userProfile && parsedData.userProfile.educations) {
            // Ajouter la nouvelle formation
            parsedData.userProfile.educations.push(education);
            localStorage.setItem('userData', JSON.stringify(parsedData));
            console.log('Formation ajoutée au localStorage');
          }
        }

        this.loadUser();
      },
      (error) => {
        console.error('Erreur lors de l\'ajout de la formation:', error);
        alert('Erreur lors de l\'ajout de la formation. Veuillez réessayer.');
      }
    );
  }

  // Certification Management Methods
  addCertification() {
    const dialogRef = this.dialog.open(AddCertificationModalComponent, {
      width: '400px',
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        console.log('Certificat ajouté:', result);
        this.updateProfileWithCertification(result);
      }
    });
  }

  editCertification(certification: any, index: number) {
    const dialogRef = this.dialog.open(AddCertificationModalComponent, {
      width: '400px',
      data: certification
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        console.log('Certificat mis à jour:', result);
        this.userService.editCertification(result, index).subscribe(
          () => {
            console.log('Certificat mis à jour avec succès');
            this.loadUser();
          },
          (error) => {
            console.error('Erreur lors de la mise à jour du certificat:', error);
            alert('Erreur lors de la mise à jour du certificat. Veuillez réessayer.');
          }
        );
      }
    });
  }

  deleteCertification(index: number) {
    this.confirmationService.confirmDeletion('ce certificat').subscribe(confirmed => {
      if (confirmed) {
        this.userService.deleteCertification(index).subscribe(
          () => {
            console.log('Certificat supprimé avec succès');
            this.loadUser();
          },
          (error) => {
            console.error('Erreur lors de la suppression du certificat:', error);
            alert('Erreur lors de la suppression du certificat. Veuillez réessayer.');
          }
        );
      }
    });
  }

  private updateProfileWithCertification(certification: any) {
    this.userService.addCertification(certification).subscribe(
      (response: any) => {
        console.log('Profil mis à jour avec le certificat:', response);

        // Mettre à jour le localStorage avec la nouvelle certification
        const savedData = localStorage.getItem('userData');
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          if (parsedData.userProfile && parsedData.userProfile.certifications) {
            // Ajouter la nouvelle certification
            parsedData.userProfile.certifications.push(certification);
            localStorage.setItem('userData', JSON.stringify(parsedData));
            console.log('Certification ajoutée au localStorage');
          }
        }

        this.loadUser();
      },
      (error) => {
        console.error('Erreur lors de l\'ajout du certificat:', error);
        alert('Erreur lors de l\'ajout du certificat. Veuillez réessayer.');
      }
    );
  }

  // Experience Management Methods
  addExperience() {
    const dialogRef = this.dialog.open(AddExperienceModalComponent, {
      width: '400px',
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        console.log('Expérience ajoutée:', result);
        this.updateProfileWithExperience(result);
      }
    });
  }

  editExperience(experience: any, index: number) {
    const dialogRef = this.dialog.open(AddExperienceModalComponent, {
      width: '400px',
      data: experience
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        console.log('Expérience mise à jour:', result);
        this.userService.editExperience(result, index).subscribe(
          () => {
            console.log('Expérience mise à jour avec succès');
            this.loadUser();
          },
          (error) => {
            console.error('Erreur lors de la mise à jour de l\'expérience:', error);
            alert('Erreur lors de la mise à jour de l\'expérience. Veuillez réessayer.');
          }
        );
      }
    });
  }

  deleteExperience(index: number) {
    this.confirmationService.confirmDeletion('cette expérience').subscribe(confirmed => {
      if (confirmed) {
        this.userService.deleteExperience(index).subscribe(
          () => {
            console.log('Expérience supprimée avec succès');
            this.loadUser();
          },
          (error) => {
            console.error('Erreur lors de la suppression de l\'expérience:', error);
            alert('Erreur lors de la suppression de l\'expérience. Veuillez réessayer.');
          }
        );
      }
    });
  }

  private updateProfileWithExperience(experience: any) {
    this.userService.addExperience(experience).subscribe(
      (response: any) => {
        console.log('Profil mis à jour avec l\'expérience:', response);

        // Mettre à jour le localStorage avec la nouvelle expérience
        const savedData = localStorage.getItem('userData');
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          if (parsedData.userProfile && parsedData.userProfile.experiences) {
            // Ajouter la nouvelle expérience
            parsedData.userProfile.experiences.push(experience);
            localStorage.setItem('userData', JSON.stringify(parsedData));
            console.log('Expérience ajoutée au localStorage');
          }
        }

        this.loadUser();
      },
      (error) => {
        console.error('Erreur lors de l\'ajout de l\'expérience:', error);
        alert('Erreur lors de l\'ajout de l\'expérience. Veuillez réessayer.');
      }
    );
  }

  // Skill Management Methods
  addSkill(): void {
    const dialogRef = this.dialog.open(AddSkillModalComponent, {
      width: '400px',
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        console.log('Compétence à ajouter:', result);
        this.userService.addSkill(result).subscribe(
          (response: any) => {
            console.log('Compétence ajoutée avec succès:', response);
            this.loadUser();
          },
          (error) => {
            console.error('Erreur lors de l\'ajout de la compétence:', error);
            alert('Erreur lors de l\'ajout de la compétence. Veuillez réessayer.');
          }
        );
      }
    });
  }

  editSkill(skill: any, index: number) {
    const dialogRef = this.dialog.open(AddSkillModalComponent, {
      width: '400px',
      data: skill
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        console.log('Compétence mise à jour:', result);
        this.userService.editSkill(result, index).subscribe(
          () => {
            console.log('Compétence mise à jour avec succès');
            this.loadUser();
          },
          (error) => {
            console.error('Erreur lors de la mise à jour de la compétence:', error);
            alert('Erreur lors  lors de la mise à jour de la compétence');
          }
        );
      }
    });
  }

  deleteSkill(index: number) {
    this.confirmationService.confirmDeletion('cette compétence').subscribe(confirmed => {
      if (confirmed) {
        this.userService.deleteSkill(index).subscribe(
          () => {
            console.log('Compétence supprimée avec succès');
            this.loadUser();
          },
          (error) => {
            console.error('Erreur lors de la suppression de la compétence:', error);
            alert('Erreur lors de la suppression de la compétence. Veuillez réessayer.');
          }
        );
      }
    });
  }

  // Méthode supprimée car intégrée directement dans addSkill

  // Link Management Methods
  addLink() {
    const dialogRef = this.dialog.open(AddLinkModalComponent, {
      width: '250px',
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        console.log('Lien ajouté:', result);
        this.updateProfileWithLink(result);
      }
    });
  }

  editLink(link: any, index: number) {
    const dialogRef = this.dialog.open(AddLinkModalComponent, {
      width: '250px',
      data: link
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        console.log('Lien mis à jour:', result);
        this.userService.editLink(result, index).subscribe(
          () => {
            console.log('Lien mis à jour avec succès');
            this.loadUser();
          },
          (error) => {
            console.error('Erreur lors de la mise à jour du lien:', error);
            alert('Erreur lors de la mise à jour du lien. Veuillez réessayer.');
          }
        );
      }
    });
  }

  deleteLink(index: number) {
    this.confirmationService.confirmDeletion('ce lien').subscribe(confirmed => {
      if (confirmed) {
        this.userService.deleteLink(index).subscribe(
          () => {
            console.log('Lien supprimé avec succès');
            this.loadUser();
          },
          (error) => {
            console.error('Erreur lors de la suppression du lien:', error);
            alert('Erreur lors de la suppression du lien. Veuillez réessayer.');
          }
        );
      }
    });
  }

  private updateProfileWithLink(link: any) {
    this.userService.addLink(link).subscribe(
      (response: any) => {
        console.log('Profil mis à jour avec le lien:', response);
        this.loadUser();
      },
      (error) => {
        console.error('Erreur lors de l\'ajout du lien:', error);
        alert('Erreur lors de l\'ajout du lien. Veuillez réessayer.');
      }
    );
  }

  // Photo Upload Methods
  openFileInput() {
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        this.photoUploadError = 'Veuillez sélectionner une image (JPEG, PNG, GIF).';
        return;
      }
      if (file.size > 2 * 1024 * 1024) {
        this.photoUploadError = 'Le fichier est trop volumineux. La taille maximale est de 2 Mo.';
        return;
      }
      this.photoUploadError = null;
      this.uploadPhoto(file);
    }
  }

  uploadPhoto(file: File) {
    this.isUploadingPhoto = true;
    this.userService.uploadPhoto(file).subscribe(
      (response: any) => {
        console.log('Photo téléchargée avec succès:', response);
        // Utiliser le service AvatarService pour obtenir l'URL correcte
        this.avatarUrl = this.avatarService.getAvatarUrl(response.photoUrl);
        console.log('Nouvelle photo URL:', this.avatarUrl);
        this.isUploadingPhoto = false;
        this.loadUser();
      },
      (error) => {
        console.error('Erreur lors de l\'upload de la photo:', error);
        this.isUploadingPhoto = false;

        // Essayer d'extraire un message d'erreur détaillé de la réponse
        let errorMessage = 'Une erreur est survenue lors de l\'upload de la photo.';

        if (error.error && error.error.error) {
          // Si le backend renvoie un objet avec une propriété 'error'
          errorMessage = error.error.error;
        } else if (typeof error.error === 'string') {
          // Si le backend renvoie une chaîne de caractères
          errorMessage = error.error;
        } else if (error.message) {
          // Si l'erreur a une propriété 'message'
          errorMessage = error.message;
        }

        this.photoUploadError = errorMessage;
      }
    );
  }

  // Profile Edit Method
  editProfile(): void {
    const dialogRef = this.dialog.open(EditProfileComponent, {
      width: '600px',
      data: { user: this.user, userProfile: this.userProfile }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'updated') {
        this.loadUser();
      }
    });
  }

  // Bio Edit Methods
  @ViewChild('bioTextarea') bioTextarea!: ElementRef;

  startEditingBio(): void {
    this.isEditingBio = true;
    this.tempBio = this.user?.bio || '';

    // Focus sur le textarea après le rendu
    setTimeout(() => {
      if (this.bioTextarea) {
        this.bioTextarea.nativeElement.focus();
      }
    }, 100);
  }

  cancelEditingBio(): void {
    this.isEditingBio = false;
    this.tempBio = this.user?.bio || '';
  }

  saveBio(): void {
    if (this.isSavingBio) return;

    this.isSavingBio = true;
    console.log('Sauvegarde de la bio:', this.tempBio);

    // Vérifier si l'utilisateur est connecté
    if (!this.user || !this.user.id) {
      console.error('Utilisateur non connecté ou ID manquant');
      this.isSavingBio = false;
      alert('Erreur: Utilisateur non connecté. Veuillez vous reconnecter.');
      return;
    }

    const updatedProfile = {
      bio: this.tempBio.trim(),
      userId: this.user.id // Assurez-vous que l'ID utilisateur est inclus
    };

    console.log('Envoi de la mise à jour:', updatedProfile);

    this.userService.updateFullUserProfile(updatedProfile).subscribe({
      next: (response) => {
        console.log('Bio mise à jour avec succès:', response);
        this.isSavingBio = false;
        this.isEditingBio = false;

        // Mettre à jour la bio localement pour un feedback immédiat
        if (this.user) {
          this.user.bio = this.tempBio.trim();

          // Mettre à jour les données dans le localStorage
          const savedData = localStorage.getItem('userData');
          if (savedData) {
            const parsedData = JSON.parse(savedData);
            if (parsedData.user) {
              parsedData.user.bio = this.tempBio.trim();
              localStorage.setItem('userData', JSON.stringify(parsedData));
              console.log('Données mises à jour dans le localStorage');
            }
          }
        }
      },
      error: (error) => {
        console.error('Erreur lors de la mise à jour de la bio:', error);
        this.isSavingBio = false;
        alert('Erreur lors de la mise à jour de la bio: ' + (error.message || 'Veuillez réessayer.'));
      }
    });
  }

  // Update bio from bio-section component
  onBioUpdated(newBio: string): void {
    if (this.user) {
      this.user.bio = newBio;
    }
    // Ne pas appeler loadUser() ici pour éviter des rechargements inutiles
    // La bio est déjà mise à jour localement
  }

  // CV Management Methods
  addCV() {
    // Ouvrir le modal d'ajout de CV
    const dialogRef = this.dialog.open(AddCvModalComponent, {
      width: '550px',
      data: { userEmail: this.user?.email }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.success) {
        console.log('CV ajouté avec succès:', result);

        // Mettre à jour les données dans le localStorage avant de recharger
        const savedData = localStorage.getItem('userData');
        if (savedData && result.cvData) {
          const parsedData = JSON.parse(savedData);
          if (parsedData.userProfile) {
            // Ajouter les données du CV au profil utilisateur dans le localStorage
            parsedData.userProfile.cv = {
              name: result.cvData.name,
              url: result.cvData.url || `cv_${this.user?.id}_${new Date().getTime()}`,
              uploadDate: new Date().toISOString()
            };
            localStorage.setItem('userData', JSON.stringify(parsedData));
            console.log('Données CV mises à jour dans le localStorage');
          }
        }

        // Afficher un message de succès temporaire
        const successMessage = document.createElement('div');
        successMessage.className = 'cv-success-message';
        successMessage.innerHTML = '<mat-icon>check_circle</mat-icon> CV ajouté avec succès!';
        document.body.appendChild(successMessage);

        // Supprimer le message après 3 secondes
        setTimeout(() => {
          document.body.removeChild(successMessage);
        }, 3000);

        // Recharger les données utilisateur pour afficher le nouveau CV
        this.loadUser();
      }
    });
  }

  // Méthode pour calculer le pourcentage de complétion du profil
  getProfileCompletionPercentage(): number {
    if (!this.user || !this.userProfile) return 0;

    // Définir les champs obligatoires et optionnels
    const requiredFields = [
      !!this.user.firstName,
      !!this.user.lastName,
      !!this.user.email
    ];

    // Définir les champs optionnels qui améliorent le profil
    const optionalFields = [
      !!this.user.phoneNumber,
      !!this.user.address,
      !!this.user.dateOfBirth,
      !!this.userProfile.cv,
      this.userProfile.skills && this.userProfile.skills.length > 0,
      this.userProfile.certifications && this.userProfile.certifications.length > 0,
      this.userProfile.educations && this.userProfile.educations.length > 0,
      this.userProfile.experiences && this.userProfile.experiences.length > 0,
      this.userProfile.links && this.userProfile.links.length > 0
    ];

    // Calculer le nombre de champs remplis
    const requiredFieldsCount = requiredFields.filter(Boolean).length;
    const optionalFieldsCount = optionalFields.filter(Boolean).length;

    // Calculer le pourcentage (les champs obligatoires comptent pour 40%, les optionnels pour 60%)
    const requiredPercentage = (requiredFieldsCount / requiredFields.length) * 40;
    const optionalPercentage = (optionalFieldsCount / optionalFields.length) * 60;

    // Retourner le pourcentage total arrondi
    return Math.round(requiredPercentage + optionalPercentage);
  }

  // Méthode pour obtenir les éléments manquants du profil
  getMissingProfileItems(): string[] {
    const missingItems: string[] = [];

    if (!this.user) return missingItems;

    // Vérifier les informations personnelles
    if (!this.user.phoneNumber) missingItems.push('Numéro de téléphone');
    if (!this.user.address) missingItems.push('Adresse');
    if (!this.user.dateOfBirth) missingItems.push('Date de naissance');

    // Vérifier les éléments du profil
    if (!this.userProfile) return missingItems;

    if (!this.userProfile.cv) missingItems.push('CV');
    if (!this.userProfile.skills || this.userProfile.skills.length === 0) missingItems.push('Compétences');
    if (!this.userProfile.certifications || this.userProfile.certifications.length === 0) missingItems.push('Certifications');
    if (!this.userProfile.educations || this.userProfile.educations.length === 0) missingItems.push('Formations');
    if (!this.userProfile.experiences || this.userProfile.experiences.length === 0) missingItems.push('Expériences');
    if (!this.userProfile.links || this.userProfile.links.length === 0) missingItems.push('Liens');

    return missingItems;
  }

  // Méthode pour ouvrir le modal de complétion du profil
  openCompleteProfileModal(): void {
    const missingItems = this.getMissingProfileItems();

    const dialogRef = this.dialog.open(CompleteProfileModalComponent, {
      width: '800px',
      maxHeight: '90vh',
      data: {
        user: this.user,
        userProfile: this.userProfile,
        missingItems: missingItems
      },
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'updated') {
        this.loadUser(); // Recharger les données du profil
      }
    });
  }

  // Méthode pour compléter le profil en fonction de l'élément manquant sélectionné
  completeProfileItem(item: string): void {
    switch(item) {
      case 'Numéro de téléphone':
      case 'Adresse':
      case 'Date de naissance':
        this.editProfile();
        break;
      case 'CV':
        this.addCV();
        break;
      case 'Compétences':
        this.addSkill();
        break;
      case 'Certifications':
        this.addCertification();
        break;
      case 'Formations':
        this.addEducation();
        break;
      case 'Expériences':
        this.addExperience();
        break;
      case 'Liens':
        this.addLink();
        break;
      default:
        this.editProfile();
        break;
    }
  }
}
