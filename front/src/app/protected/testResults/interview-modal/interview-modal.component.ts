import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { NgIf } from '@angular/common';
import { TestResultDisplay, InterviewRequest } from '../../../services/test-result/test-result.service';

export interface InterviewModalData {
  candidate: TestResultDisplay;
}

@Component({
  selector: 'app-interview-modal',
  standalone: true,
  imports: [
    MatDialogModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    NgIf
  ],
  template: `
    <div class="interview-modal">
      <div class="modal-header">
        <h2 mat-dialog-title>
          <mat-icon>event</mat-icon>
          Planifier un entretien
        </h2>
        <button mat-icon-button mat-dialog-close class="close-button">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <div mat-dialog-content class="modal-content">
        <div class="candidate-info">
          <div class="candidate-avatar">
            <img [src]="data.candidate.profileImage" [alt]="data.candidate.name" />
          </div>
          <div class="candidate-details">
            <h3>{{ data.candidate.name }}</h3>
            <p>{{ data.candidate.job }}</p>
            <div class="candidate-score">
              <span class="score-label">Score:</span>
              <span class="score-value" [style.color]="getScoreColor(data.candidate.score)">
                {{ data.candidate.score }}
              </span>
            </div>
          </div>
        </div>

        <form [formGroup]="interviewForm" class="interview-form">
          <!-- Date et heure -->
          <div class="form-row">
            <mat-form-field appearance="outline" class="date-field">
              <mat-label>Date de l'entretien</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="interviewDate" required>
              <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error *ngIf="interviewForm.get('interviewDate')?.hasError('required')">
                La date est obligatoire
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="time-field">
              <mat-label>Heure</mat-label>
              <input matInput type="time" formControlName="interviewTime" required>
              <mat-error *ngIf="interviewForm.get('interviewTime')?.hasError('required')">
                L'heure est obligatoire
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Type d'entretien -->
          <mat-form-field appearance="outline">
            <mat-label>Type d'entretien</mat-label>
            <mat-select formControlName="interviewType" required>
              <mat-option value="presentiel">Présentiel</mat-option>
              <mat-option value="visio">Visioconférence</mat-option>
              <mat-option value="telephonique">Téléphonique</mat-option>
            </mat-select>
            <mat-error *ngIf="interviewForm.get('interviewType')?.hasError('required')">
              Le type d'entretien est obligatoire
            </mat-error>
          </mat-form-field>

          <!-- Lieu -->
          <mat-form-field appearance="outline">
            <mat-label>Lieu / Lien de connexion</mat-label>
            <input matInput formControlName="location" required>
            <mat-hint>Adresse pour présentiel, lien pour visio, numéro pour téléphone</mat-hint>
            <mat-error *ngIf="interviewForm.get('location')?.hasError('required')">
              Le lieu est obligatoire
            </mat-error>
          </mat-form-field>

          <!-- Informations recruteur -->
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Nom du recruteur</mat-label>
              <input matInput formControlName="recruiterName" required>
              <mat-error *ngIf="interviewForm.get('recruiterName')?.hasError('required')">
                Le nom du recruteur est obligatoire
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Email du recruteur</mat-label>
              <input matInput type="email" formControlName="recruiterEmail" required>
              <mat-error *ngIf="interviewForm.get('recruiterEmail')?.hasError('required')">
                L'email du recruteur est obligatoire
              </mat-error>
              <mat-error *ngIf="interviewForm.get('recruiterEmail')?.hasError('email')">
                Format d'email invalide
              </mat-error>
            </mat-form-field>
          </div>

          <!-- Entreprise -->
          <mat-form-field appearance="outline">
            <mat-label>Nom de l'entreprise</mat-label>
            <input matInput formControlName="companyName" required>
            <mat-error *ngIf="interviewForm.get('companyName')?.hasError('required')">
              Le nom de l'entreprise est obligatoire
            </mat-error>
          </mat-form-field>

          <!-- Message personnalisé -->
          <mat-form-field appearance="outline">
            <mat-label>Message personnalisé (optionnel)</mat-label>
            <textarea matInput rows="4" formControlName="message" 
                      placeholder="Ajoutez des informations supplémentaires pour le candidat..."></textarea>
          </mat-form-field>
        </form>
      </div>

      <div mat-dialog-actions class="modal-actions">
        <button mat-button mat-dialog-close class="cancel-button">
          <mat-icon>cancel</mat-icon>
          Annuler
        </button>
        <button mat-raised-button 
                color="primary" 
                (click)="scheduleInterview()" 
                [disabled]="interviewForm.invalid || isLoading"
                class="schedule-button">
          <mat-icon *ngIf="!isLoading">send</mat-icon>
          <mat-icon *ngIf="isLoading" class="loading-icon">hourglass_empty</mat-icon>
          {{ isLoading ? 'Envoi en cours...' : 'Planifier l\'entretien' }}
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./interview-modal.component.css']
})
export class InterviewModalComponent {
  interviewForm: FormGroup;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<InterviewModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: InterviewModalData
  ) {
    this.interviewForm = this.createForm();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      interviewDate: ['', Validators.required],
      interviewTime: ['', Validators.required],
      interviewType: ['presentiel', Validators.required],
      location: ['', Validators.required],
      recruiterName: ['', Validators.required],
      recruiterEmail: ['', [Validators.required, Validators.email]],
      companyName: ['', Validators.required],
      message: ['']
    });
  }

  getScoreColor(score: string): string {
    const percentage = parseInt(score.replace('%', ''));
    if (percentage >= 80) return '#10B981';
    if (percentage >= 60) return '#F59E0B';
    if (percentage >= 40) return '#EF4444';
    return '#6B7280';
  }

  scheduleInterview() {
    if (this.interviewForm.valid) {
      this.isLoading = true;
      
      const formValue = this.interviewForm.value;
      const interviewRequest: InterviewRequest = {
        candidateId: this.data.candidate.userId,
        candidateName: this.data.candidate.name,
        candidateEmail: '<EMAIL>', // À récupérer depuis les données utilisateur
        testResultId: this.data.candidate.id,
        interviewDate: formValue.interviewDate,
        interviewTime: formValue.interviewTime,
        location: formValue.location,
        interviewType: formValue.interviewType,
        message: formValue.message,
        recruiterName: formValue.recruiterName,
        recruiterEmail: formValue.recruiterEmail,
        companyName: formValue.companyName
      };

      // Simuler un délai d'envoi
      setTimeout(() => {
        this.isLoading = false;
        this.dialogRef.close(interviewRequest);
      }, 2000);
    }
  }
}
