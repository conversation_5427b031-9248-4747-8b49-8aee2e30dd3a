<div class="results-page">
  <app-sidebar></app-sidebar>

  <div class="results-container">
    <header class="results-header">
      <h1>Test Results</h1>
      <div class="header-actions">
        <!-- Sélecteur de tri avancé -->
        <div class="sort-selector">
          <label>Trier par :</label>
          <mat-select [value]="selectedSortOption"
                     (selectionChange)="onSortOptionChange($event.value)"
                     class="sort-select">
            <mat-option *ngFor="let option of sortOptions" [value]="option">
              {{ option.label }}
            </mat-option>
          </mat-select>
        </div>

        <button mat-button class="refresh-button" (click)="refreshResults()" [disabled]="loading">
          <mat-icon>refresh</mat-icon> Actualiser
        </button>
        <button mat-button class="test-api-button" (click)="testApiConnection()" [disabled]="loading">
          <mat-icon>cloud_done</mat-icon> Test API
        </button>
        <button mat-button class="test-no-auth-button" (click)="testWithoutAuth()" [disabled]="loading">
          <mat-icon>cloud_off</mat-icon> Test Sans Auth
        </button>
        <button mat-button class="filter-button" (click)="toggleFilters()">
          <mat-icon>filter_list</mat-icon> Filtres {{ showFilters ? '▲' : '▼' }}
        </button>
        <button mat-button class="reset-button" (click)="resetFilters()">
          <mat-icon>clear_all</mat-icon> Reset
        </button>
      </div>
    </header>

    <!-- Section des filtres -->
    <div class="filters-section" *ngIf="showFilters">
      <mat-card class="filters-card">
        <mat-card-header>
          <mat-card-title>Filtres de recherche</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="filters-grid">

            <!-- Filtre par lieu -->
            <div class="filter-group">
              <label>Lieu :</label>
              <mat-select [(value)]="filters.location" (selectionChange)="onFilterChange()">
                <mat-option value="all">Tous les lieux</mat-option>
                <mat-option *ngFor="let location of locations" [value]="location">
                  {{ location }}
                </mat-option>
              </mat-select>
            </div>

            <!-- Filtre par expérience -->
            <div class="filter-group">
              <label>Expérience :</label>
              <mat-select [(value)]="filters.experience" (selectionChange)="onFilterChange()">
                <mat-option value="all">Tous niveaux</mat-option>
                <mat-option *ngFor="let exp of experiences" [value]="exp">
                  {{ exp }}
                </mat-option>
              </mat-select>
            </div>

            <!-- Filtre par score -->
            <div class="filter-group score-filter">
              <label>Score : {{ filters.minScore }}% - {{ filters.maxScore }}%</label>
              <div class="score-sliders">
                <div class="slider-group">
                  <label>Min:</label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    step="5"
                    [(ngModel)]="filters.minScore"
                    (input)="onFilterChange()"
                    class="score-slider">
                </div>
                <div class="slider-group">
                  <label>Max:</label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    step="5"
                    [(ngModel)]="filters.maxScore"
                    (input)="onFilterChange()"
                    class="score-slider">
                </div>
              </div>
            </div>

            <!-- Filtre par compétences -->
            <div class="filter-group skills-filter">
              <label>Compétences :</label>
              <div class="skills-chips">
                <div
                  class="skill-chip"
                  *ngFor="let skill of skills"
                  [class.selected]="isSkillSelected(skill)"
                  (click)="toggleSkill(skill)">
                  {{ skill }}
                </div>
              </div>
            </div>

          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- En-têtes de tri -->
    <div class="sort-headers" *ngIf="!loading && !error">
      <div class="sort-header" (click)="onSortChange('name')">
        <span>Nom</span>
        <mat-icon>{{ getSortIcon('name') }}</mat-icon>
      </div>
      <div class="sort-header" (click)="onSortChange('score')">
        <span>Score</span>
        <mat-icon>{{ getSortIcon('score') }}</mat-icon>
      </div>
      <div class="sort-header" (click)="onSortChange('location')">
        <span>Lieu</span>
        <mat-icon>{{ getSortIcon('location') }}</mat-icon>
      </div>
      <div class="sort-header" (click)="onSortChange('date')">
        <span>Date</span>
        <mat-icon>{{ getSortIcon('date') }}</mat-icon>
      </div>
    </div>

    <!-- Loading indicator -->
    <div *ngIf="loading" class="loading-container">
      <div class="loading-content">
        <mat-icon class="loading-spinner">sync</mat-icon>
        <h3>{{ isEnriching ? 'Enrichissement des profils candidats' : 'Récupération des résultats de test' }}</h3>
        <p>{{ isEnriching ? 'Récupération des informations détaillées des candidats...' : 'Chargement des données depuis la base de données...' }}</p>
        <div class="loading-progress">
          <div class="progress-bar" [style.width.%]="enrichmentProgress"></div>
        </div>
        <div *ngIf="isEnriching" class="enrichment-details">
          <small>Progression : {{ enrichmentProgress }}%</small>
        </div>
      </div>
    </div>

    <!-- Error message -->
    <div *ngIf="error && !loading" class="error-container">
      <div class="error-content">
        <mat-icon class="error-icon">error_outline</mat-icon>
        <h3>Impossible de charger les résultats</h3>
        <p>{{ error }}</p>
        <div class="error-actions">
          <button mat-raised-button color="primary" (click)="refreshResults()">
            <mat-icon>refresh</mat-icon> Réessayer
          </button>
          <button mat-button (click)="loadTestResultsPaginated()">
            <mat-icon>cloud_ serve
              download</mat-icon> Recharger depuis l'API
          </button>
        </div>
      </div>
    </div>

    <!-- Results grid -->
    <div *ngIf="!loading && !error" class="results-grid">
      <mat-card class="result-card" *ngFor="let result of results">
        <mat-card-header class="result-header">
          <mat-card-title>{{ result.job }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="candidate-info">
            <!-- Image de profil cliquable -->
            <div class="avatar-container" (click)="goToProfile(result.userId)"
                 title="Cliquer pour voir le profil de {{ result.name }}">
              <img
                [src]="result.profileImage"
                [alt]="result.name"
                class="avatar-image"
                (error)="onImageError($event)"
              />
              <div class="avatar-overlay">
                <mat-icon>visibility</mat-icon>
                <span class="overlay-text">Voir profil</span>
              </div>
            </div>
            <div class="candidate-details">
              <p><strong>{{ result.name }}</strong></p>
              <p><mat-icon>location_on</mat-icon> {{ result.location }}</p>
              <p><mat-icon>work</mat-icon> {{ result.experience }}</p>
              <p><mat-icon>event</mat-icon> {{ result.date }}</p>
            </div>
          </div>

          <!-- Compétences -->
          <div class="skills-section">
            <label>Compétences :</label>
            <div class="skills-tags">
              <span class="skill-tag" *ngFor="let skill of result.skills.slice(0, 3)">
                {{ skill }}
              </span>
              <span class="skill-more" *ngIf="result.skills.length > 3">
                +{{ result.skills.length - 3 }}
              </span>
            </div>
          </div>

          <div class="test-info">
            <div class="time-limit">
              <p><mat-icon>schedule</mat-icon> Time Limit: <span class="red-text">{{ result.timeLimit }}</span></p>
            </div>
            <p><mat-icon>timer</mat-icon> Time Passed: {{ result.timePassed }}</p>
          </div>

          <div class="score-section">
            <div class="score" [style.color]="getScoreColor(result.score)">
              <strong>Score: {{ result.score }}</strong>
              <span class="score-status">({{ getScoreStatus(result.score) }})</span>
            </div>
            <div class="score-details">
              <p><mat-icon>check_circle</mat-icon> {{ result.correctAnswers }}/{{ result.totalQuestions }} questions correctes</p>
            </div>
          </div>
        </mat-card-content>
        <div class="result-actions">
          <!-- Actions candidat -->
          <div class="candidate-actions">
            <!-- Bouton Accepter -->
            <button mat-raised-button
                    color="primary"
                    class="accept-btn"
                    [disabled]="isCandidateAccepted(result.userId) || isCandidateRejected(result.userId)"
                    (click)="acceptCandidate(result)"
                    title="Accepter le candidat et envoyer un email d'entretien">
              <mat-icon>check_circle</mat-icon>
              {{ isCandidateAccepted(result.userId) ? 'Accepté' : 'Accepter' }}
            </button>

            <!-- Bouton Rejeter -->
            <button mat-raised-button
                    color="warn"
                    class="reject-btn"
                    [disabled]="isCandidateAccepted(result.userId) || isCandidateRejected(result.userId)"
                    (click)="rejectCandidate(result)"
                    title="Rejeter le candidat">
              <mat-icon>cancel</mat-icon>
              {{ isCandidateRejected(result.userId) ? 'Rejeté' : 'Rejeter' }}
            </button>
          </div>

          <!-- Actions secondaires -->
          <div class="secondary-actions">
            <button mat-icon-button color="primary" title="Voir le profil" (click)="goToProfile(result.userId)">
              <mat-icon>person</mat-icon>
            </button>
            <button mat-icon-button color="accent" title="Voir les détails">
              <mat-icon>visibility</mat-icon>
            </button>
          </div>

          <!-- Statut du candidat -->
          <div class="candidate-status" *ngIf="getCandidateAction(result.userId) !== 'pending'">
            <span class="status-badge"
                  [class.accepted]="isCandidateAccepted(result.userId)"
                  [class.rejected]="isCandidateRejected(result.userId)">
              <mat-icon>{{ isCandidateAccepted(result.userId) ? 'check_circle' : 'cancel' }}</mat-icon>
              {{ isCandidateAccepted(result.userId) ? 'Candidat accepté' : 'Candidat rejeté' }}
            </span>
          </div>
        </div>
      </mat-card>

      <!-- Empty state -->
      <div *ngIf="results.length === 0" class="empty-state">
        <mat-icon>assignment</mat-icon>
        <h3>Aucun résultat trouvé</h3>
        <p>Aucun candidat ne correspond aux critères de filtrage sélectionnés.</p>
        <button mat-button (click)="resetFilters()">Réinitialiser les filtres</button>
      </div>
    </div>

    <!-- Pagination dynamique -->
    <footer class="pagination-footer" *ngIf="paginatedResponse">
      <!-- Informations de pagination -->
      <div class="pagination-info">
        <span class="total-results">{{ paginatedResponse.totalElements }} résultats au total</span>
        <span class="page-info">Page {{ paginatedResponse.currentPage + 1 }} sur {{ paginatedResponse.totalPages }}</span>
      </div>

      <!-- Sélecteur de taille de page -->
      <div class="page-size-selector">
        <label>Résultats par page:</label>
        <mat-select [value]="paginationOptions.size" (selectionChange)="changePageSize($event.value)">
          <mat-option [value]="5">5</mat-option>
          <mat-option [value]="10">10</mat-option>
          <mat-option [value]="20">20</mat-option>
          <mat-option [value]="50">50</mat-option>
        </mat-select>
      </div>

      <!-- Contrôles de pagination -->
      <div class="pagination-controls">
        <!-- Première page -->
        <button mat-icon-button
                class="pagination-button"
                (click)="goToPage(0)"
                [disabled]="paginatedResponse.isFirst"
                title="Première page">
          <mat-icon>first_page</mat-icon>
        </button>

        <!-- Page précédente -->
        <button mat-icon-button
                class="pagination-button"
                (click)="prevPage()"
                [disabled]="!paginatedResponse.hasPrevious"
                title="Page précédente">
          <mat-icon>chevron_left</mat-icon>
        </button>

        <!-- Numéros de page -->
        <div class="pagination-page"
             *ngFor="let page of getPageNumbers()"
             [class.active]="isCurrentPage(page)"
             (click)="goToPage(page)"
             [title]="'Page ' + (page + 1)">
          {{ page + 1 }}
        </div>

        <!-- Page suivante -->
        <button mat-icon-button
                class="pagination-button"
                (click)="nextPage()"
                [disabled]="!paginatedResponse.hasNext"
                title="Page suivante">
          <mat-icon>chevron_right</mat-icon>
        </button>

        <!-- Dernière page -->
        <button mat-icon-button
                class="pagination-button"
                (click)="goToPage(paginatedResponse.totalPages - 1)"
                [disabled]="paginatedResponse.isLast"
                title="Dernière page">
          <mat-icon>last_page</mat-icon>
        </button>
      </div>
    </footer>

  </div>
</div>
