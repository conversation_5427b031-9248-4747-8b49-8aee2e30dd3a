/* ==========================================================================
   Réinitialisation et styles globaux
   ========================================================================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Roboto, Segoe UI, sans-serif;
  background-color: #FFFFFF; /* Fond blanc pour la page */
  color: #1C2526; /* Texte sombre pour contraste */
}

/* ==========================================================================
   Conteneur principal de la page
   ========================================================================== */
.results-page {
  display: flex;
  min-height: 100vh;
}

/* ==========================================================================
   Conteneur des résultats
   ========================================================================== */
.results-container {
  margin-left: 250px;
  padding: 24px;
  width: calc(100% - 250px);
  background-color: #FFFFFF; /* Fond blanc pour cohérence */
}

/* ==========================================================================
   En-tête
   ========================================================================== */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.reset-button {
  background-color: #6B7280 !important;
  color: white !important;
  border-radius: 6px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.reset-button:hover:not([disabled]) {
  background-color: #4B5563 !important;
  transform: translateY(-1px);
}

.results-header h1 {
  font-size: 26px;
  font-weight: 600;
  color: #1C2526; /* Texte sombre pour les titres */
  letter-spacing: -0.02em;
}

.results-header .filter-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #F3F2EF; /* Gris clair inspiré du tableau de bord */
  color: #1C2526; /* Texte sombre */
  border-radius: 6px;
  font-size: 14px;
  font-weight: 400;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.results-header .filter-button:hover {
  background-color: #E5E7EB; /* Gris légèrement plus foncé au survol */
  color: #0A66C2; /* Bleu professionnel pour interaction */
}

.results-header .filter-button mat-icon {
  margin-right: 8px;
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #1C2526; /* Icône sombre */
}

/* ==========================================================================
   Grille des résultats
   ========================================================================== */
.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

/* ==========================================================================
   Carte de résultat
   ========================================================================== */
.result-card {
  background-color: #FFFFFF; /* Fond blanc pour les cartes */
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* Ombre douce */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1); /* Ombre renforcée au survol */
}

/* ==========================================================================
   En-tête de la carte
   ========================================================================== */
.result-header {
  margin-bottom: 12px;
}

.result-header mat-card-title {
  font-size: 16px;
  font-weight: 500;
  color: #1C2526; /* Texte sombre pour les titres */
}

/* ==========================================================================
   Informations du candidat
   ========================================================================== */
.candidate-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.candidate-info .avatar {
  font-size: 36px;
  width: 36px;
  height: 36px;
  color: #FFFFFF; /* Icône blanche */
  background-color: #0A66C2; /* Bleu professionnel pour avatar */
  border-radius: 50%;
  padding: 8px;
  margin-right: 12px;
}

.candidate-info p {
  font-size: 14px;
  color: #1C2526; /* Texte sombre */
  margin: 4px 0;
}

.candidate-info mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  vertical-align: middle;
  color: #6B7280; /* Gris moyen pour icônes */
}

/* ==========================================================================
   Informations du test
   ========================================================================== */
.test-info {
  margin-bottom: 16px;
}

.test-info p {
  font-size: 14px;
  color: #1C2526; /* Texte sombre */
  display: flex;
  align-items: center;
  margin: 8px 0;
}

.test-info mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #0A66C2; /* Bleu pour icônes */
}

.test-info .time-limit .red-text {
  color: #EF4444; /* Rouge pour alerte */
}

/* ==========================================================================
   Score
   ========================================================================== */
.score-section {
  margin-top: 16px;
}

.score {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.score-status {
  font-size: 14px;
  font-weight: 400;
  margin-left: 8px;
  opacity: 0.8;
}

.score-details {
  font-size: 13px;
  color: #6B7280;
}

.score-details p {
  display: flex;
  align-items: center;
  margin: 4px 0;
}

.score-details mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  color: #22C55E;
}

/* ==========================================================================
   Actions de la carte
   ========================================================================== */
.result-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.result-actions mat-icon-button {
  color: #6B7280; /* Gris moyen par défaut */
}

.result-actions mat-icon-button[color="primary"]:hover {
  color: #0A66C2; /* Bleu au survol */
}

.result-actions mat-icon-button[color="warn"]:hover {
  color: #EF4444; /* Rouge au survol */
}

.result-actions mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* ==========================================================================
   Pagination
   ========================================================================== */
.pagination-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 16px 0;
}

.pagination-footer .pagination-button {
  background-color: #F3F2EF; /* Gris clair pour boutons */
  color: #1C2526; /* Texte sombre */
  border-radius: 6px;
  padding: 8px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.pagination-footer .pagination-button:hover:not([disabled]) {
  background-color: #E5E7EB; /* Gris légèrement plus foncé au survol */
  color: #0A66C2; /* Bleu pour interaction */
}

.pagination-footer .pagination-button[disabled] {
  background-color: #E5E7EB; /* Gris clair désactivé */
  color: #6B7280; /* Texte gris moyen */
  cursor: not-allowed;
}

.pagination-footer .pagination-button mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: #1C2526; /* Icône sombre */
}

.pagination-footer .pagination-page {
  font-size: 14px;
  color: #1C2526; /* Texte sombre */
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.pagination-footer .pagination-page:hover {
  background-color: #E5E7EB; /* Gris clair au survol */
  color: #0A66C2; /* Bleu pour interaction */
}

.pagination-footer .pagination-page.active {
  background-color: #0A66C2; /* Bleu pour page active */
  color: #FFFFFF; /* Blanc pour contraste */
  font-weight: 500;
}

/* ==========================================================================
   Responsive
   ========================================================================== */
@media (max-width: 768px) {
  .results-container {
    margin-left: 70px;
    width: calc(100% - 70px);
    padding: 16px;
  }

  .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .results-grid {
    grid-template-columns: 1fr;
  }

  .results-header .filter-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 600px) {
  .results-container {
    padding: 12px;
  }

  .results-header h1 {
    font-size: 22px;
  }

  .pagination-footer .pagination-page {
    padding: 6px 10px;
    font-size: 12px;
  }
}

/* ==========================================================================
   Loading et Error States
   ========================================================================== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
}

.loading-spinner {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #001040FF;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  font-size: 16px;
  color: #6B7280;
  margin: 0;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
  background-color: #FEF2F2;
  border-radius: 8px;
  border: 1px solid #FECACA;
}

.error-container mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

.error-container p {
  font-size: 16px;
  color: #DC2626;
  margin: 0 0 16px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
  grid-column: 1 / -1;
}

.empty-state mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: #9CA3AF;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 16px;
  color: #6B7280;
  margin: 0;
}

/* ==========================================================================
   Boutons d'action
   ========================================================================== */
.refresh-button {
  background-color: #001040FF !important;
  color: white !important;
  border-radius: 6px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.refresh-button:hover:not([disabled]) {
  background-color: #001660FF !important;
  transform: translateY(-1px);
}

.refresh-button[disabled] {
  background-color: #E5E7EB !important;
  color: #9CA3AF !important;
  cursor: not-allowed;
}

.refresh-button mat-icon {
  margin-right: 8px;
}

/* ==========================================================================
   Filters Section
   ========================================================================== */
.filters-section {
  margin-bottom: 24px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filters-card {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #E5E7EB;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.score-filter {
  grid-column: span 2;
}

.score-sliders {
  display: flex;
  gap: 20px;
  align-items: center;
}

.slider-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.slider-group label {
  font-size: 12px;
  color: #6B7280;
}

/* Styles pour les sliders natifs */
.score-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #E5E7EB;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.score-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #001040FF;
  cursor: pointer;
  border: 2px solid #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 16, 64, 0.3);
  transition: all 0.3s ease;
}

.score-slider::-webkit-slider-thumb:hover {
  background: #001660FF;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 16, 64, 0.4);
}

.score-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #001040FF;
  cursor: pointer;
  border: 2px solid #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 16, 64, 0.3);
  transition: all 0.3s ease;
}

.score-slider::-moz-range-thumb:hover {
  background: #001660FF;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 16, 64, 0.4);
}

.score-slider::-webkit-slider-track {
  width: 100%;
  height: 6px;
  cursor: pointer;
  background: linear-gradient(to right, #001040FF 0%, #FF6B35 100%);
  border-radius: 3px;
}

.score-slider::-moz-range-track {
  width: 100%;
  height: 6px;
  cursor: pointer;
  background: linear-gradient(to right, #001040FF 0%, #FF6B35 100%);
  border-radius: 3px;
  border: none;
}

.skills-filter {
  grid-column: span 2;
}

.skills-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-chip {
  display: inline-block;
  padding: 8px 16px;
  margin: 4px;
  background-color: #F3F4F6;
  color: #374151;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  border: 2px solid transparent;
  user-select: none;
}

.skill-chip:hover {
  background-color: #E5E7EB;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.skill-chip.selected {
  background-color: #001040FF !important;
  color: white !important;
  border-color: #001660FF;
  box-shadow: 0 4px 12px rgba(0, 16, 64, 0.3);
}

/* ==========================================================================
   Sort Headers
   ========================================================================== */
.sort-headers {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: #F8FAFC;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
}

.sort-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  color: #374151;
}

.sort-header:hover {
  background-color: #001040FF;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 16, 64, 0.2);
}

.sort-header mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* ==========================================================================
   Enhanced Candidate Info
   ========================================================================== */
.avatar-container {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 16px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-container:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 16, 64, 0.3);
}

.avatar-container:active {
  transform: scale(1.05);
  transition: all 0.1s ease;
}

.avatar-image {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #E5E7EB;
  transition: all 0.3s ease;
  background: #F3F4F6;
  display: block;
}

.avatar-image.fallback-avatar {
  border-color: #6B7280;
  opacity: 0.9;
}

.avatar-image:error,
.avatar-image[src=""],
.avatar-image[src="null"] {
  background: linear-gradient(135deg, #001040FF 0%, #001660FF 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
}

/* Avatar de fallback quand l'image ne charge pas */
.avatar-container.avatar-fallback {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #001040FF 0%, #001660FF 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 24px;
  border: 3px solid #E5E7EB;
  position: relative;
}

.avatar-container.avatar-fallback::before {
  content: '👤';
  font-size: 32px;
}

.avatar-container.avatar-fallback:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 16, 64, 0.3);
}

.avatar-container:hover .avatar-image {
  border-color: #001040FF;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 16, 64, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(2px);
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.avatar-overlay mat-icon {
  color: white;
  font-size: 24px;
  margin-bottom: 2px;
}

.overlay-text {
  color: white;
  font-size: 10px;
  font-weight: 600;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.candidate-details {
  flex: 1;
}

.candidate-details p {
  margin: 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.candidate-details mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #6B7280;
}

/* ==========================================================================
   Skills Section
   ========================================================================== */
.skills-section {
  margin-bottom: 16px;
}

.skills-section label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  margin-bottom: 8px;
  display: block;
}

.skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.skill-tag {
  background-color: #001040FF;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.skill-more {
  background-color: #6B7280;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* ==========================================================================
   Pagination Dynamique
   ========================================================================== */
.pagination-footer {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 32px;
  padding: 24px;
  background: #F8FAFC;
  border-radius: 12px;
  border: 1px solid #E5E7EB;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.total-results {
  font-weight: 600;
  color: #001040FF;
  font-size: 16px;
}

.page-info {
  color: #6B7280;
  font-size: 14px;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-size-selector label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
}

.page-size-selector mat-select {
  min-width: 80px;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-button {
  min-width: 40px !important;
  height: 40px;
  border-radius: 8px !important;
  background: white !important;
  color: #374151 !important;
  border: 1px solid #E5E7EB !important;
  transition: all 0.3s ease;
}

.pagination-button:hover:not([disabled]) {
  background: #001040FF !important;
  color: white !important;
  border-color: #001040FF !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 16, 64, 0.2);
}

.pagination-button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-page {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  border-radius: 8px;
  background: white;
  color: #374151;
  border: 1px solid #E5E7EB;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  user-select: none;
}

.pagination-page:hover {
  background: #F3F4F6;
  border-color: #D1D5DB;
  transform: translateY(-1px);
}

.pagination-page.active {
  background: #001040FF !important;
  color: white !important;
  border-color: #001040FF !important;
  box-shadow: 0 4px 12px rgba(0, 16, 64, 0.3);
  transform: translateY(-1px);
}

/* Responsive pour la pagination */
@media (max-width: 768px) {
  .pagination-footer {
    padding: 16px;
    gap: 16px;
  }

  .pagination-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .page-size-selector {
    align-self: stretch;
    justify-content: space-between;
  }

  .pagination-controls {
    gap: 4px;
  }

  .pagination-button {
    min-width: 36px !important;
    height: 36px;
  }

  .pagination-page {
    min-width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .total-results {
    font-size: 14px;
  }

  .page-info {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .pagination-footer {
    padding: 12px;
  }

  .pagination-controls {
    gap: 2px;
  }

  .pagination-button {
    min-width: 32px !important;
    height: 32px;
  }

  .pagination-page {
    min-width: 32px;
    height: 32px;
    font-size: 12px;
  }
}

/* ==========================================================================
   Loading & Error States
   ========================================================================== */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px;
  background: #F8FAFC;
  border-radius: 12px;
  border: 1px solid #E5E7EB;
  margin: 20px 0;
}

.loading-content,
.error-content {
  text-align: center;
  max-width: 400px;
}

.loading-spinner {
  font-size: 48px !important;
  width: 48px !important;
  height: 48px !important;
  color: #001040FF;
  animation: spin 2s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content h3 {
  color: #001040FF;
  font-size: 24px;
  font-weight: 600;
  margin: 20px 0 10px 0;
}

.loading-content p {
  color: #6B7280;
  font-size: 16px;
  margin-bottom: 30px;
}

.enrichment-details {
  margin-top: 15px;
  padding: 10px;
  background: rgba(0, 16, 64, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 16, 64, 0.1);
}

.enrichment-details small {
  color: #001040FF;
  font-weight: 600;
  font-size: 14px;
}

.loading-progress {
  width: 100%;
  height: 4px;
  background: #E5E7EB;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #001040FF, #001660FF, #FF6B35);
  border-radius: 2px;
  transition: width 0.3s ease;
  animation: progressFlow 2s ease-in-out infinite;
}

@keyframes progressFlow {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

.error-icon {
  font-size: 48px !important;
  width: 48px !important;
  height: 48px !important;
  color: #EF4444;
  margin-bottom: 20px;
}

.error-content h3 {
  color: #EF4444;
  font-size: 24px;
  font-weight: 600;
  margin: 20px 0 10px 0;
}

.error-content p {
  color: #6B7280;
  font-size: 16px;
  margin-bottom: 30px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-actions button {
  min-width: 140px;
}

/* Responsive pour les états de chargement/erreur */
@media (max-width: 768px) {
  .loading-container,
  .error-container {
    min-height: 300px;
    padding: 20px;
  }

  .loading-content h3,
  .error-content h3 {
    font-size: 20px;
  }

  .loading-spinner,
  .error-icon {
    font-size: 36px !important;
    width: 36px !important;
    height: 36px !important;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .error-actions button {
    width: 100%;
    max-width: 200px;
  }
}

/* ==========================================================================
   Sélecteur de tri avancé
   ========================================================================== */
.sort-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 16px;
}

.sort-selector label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
}

.sort-select {
  min-width: 200px;
  font-size: 14px;
}

/* ==========================================================================
   Actions candidats
   ========================================================================== */
.result-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #E5E7EB;
  background: #F8FAFC;
}

.candidate-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.accept-btn {
  background: #10B981 !important;
  color: white !important;
  min-width: 120px;
  transition: all 0.3s ease;
}

.accept-btn:hover:not([disabled]) {
  background: #059669 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.accept-btn[disabled] {
  background: #D1FAE5 !important;
  color: #065F46 !important;
  cursor: not-allowed;
}

.reject-btn {
  background: #EF4444 !important;
  color: white !important;
  min-width: 120px;
  transition: all 0.3s ease;
}

.reject-btn:hover:not([disabled]) {
  background: #DC2626 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.reject-btn[disabled] {
  background: #FEE2E2 !important;
  color: #991B1B !important;
  cursor: not-allowed;
}

.secondary-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.candidate-status {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.accepted {
  background: #D1FAE5;
  color: #065F46;
  border: 1px solid #10B981;
}

.status-badge.rejected {
  background: #FEE2E2;
  color: #991B1B;
  border: 1px solid #EF4444;
}

.status-badge mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* ==========================================================================
   Responsive pour les actions candidats
   ========================================================================== */
@media (max-width: 768px) {
  .sort-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    margin-right: 8px;
  }

  .sort-select {
    min-width: 150px;
  }

  .candidate-actions {
    flex-direction: column;
    gap: 8px;
  }

  .accept-btn,
  .reject-btn {
    width: 100%;
    min-width: auto;
  }

  .secondary-actions {
    justify-content: space-around;
  }

  .status-badge {
    font-size: 11px;
    padding: 4px 8px;
  }
}

@media (max-width: 480px) {
  .sort-selector {
    width: 100%;
    margin-bottom: 8px;
  }

  .sort-select {
    width: 100%;
    min-width: auto;
  }

  .result-actions {
    padding: 12px;
  }

  .candidate-actions {
    gap: 6px;
  }

  .accept-btn,
  .reject-btn {
    font-size: 12px;
    padding: 8px 12px;
  }
}
