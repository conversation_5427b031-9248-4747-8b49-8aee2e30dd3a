import { Component, OnInit } from '@angular/core';
import {MatIcon} from '@angular/material/icon';
import {SidebarComponent} from '../sidebar/sidebar.component';
import {MatCard, MatCardContent, MatCardHeader, MatCardTitle} from '@angular/material/card';
import {MatButton, MatIconButton} from '@angular/material/button';
import {NgForOf, NgIf} from '@angular/common';
import { TestResultService, TestResultDisplay, FilterOptions, PaginationOptions, PaginatedResponse, CandidateAction, InterviewRequest, SortOption } from '../../services/test-result/test-result.service';
import { Router } from '@angular/router';
import { MatSelect } from '@angular/material/select';
import { MatOption } from '@angular/material/core';
// MatSlider retiré - utilisation d'inputs natifs
// MatChip et MatChipSet retirés - utilisation de divs personnalisées
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-testResults',
  templateUrl: './testResults.component.html',
  standalone: true,
  imports: [
    MatIcon,
    SidebarComponent,
    MatCard,
    MatCardHeader,
    MatCardContent,
    MatIconButton,
    MatButton,
    NgForOf,
    NgIf,
    MatCardTitle,
    MatSelect,
    MatOption,
    FormsModule
  ],
  // Modifier le chemin
  styleUrls: ['./testResults.component.css'] // Modifier le chemin
})
export class TestResultsComponent implements OnInit {
  results: TestResultDisplay[] = [];
  filteredResults: TestResultDisplay[] = [];
  allResults: TestResultDisplay[] = [];
  loading = true;
  error: string | null = null;

  // Filtres
  filters: Partial<FilterOptions> = {
    location: 'all',
    minScore: 0,
    maxScore: 100,
    skills: [],
    experience: 'all'
  };

  // Options pour les filtres
  locations: string[] = [];
  skills: string[] = [];
  experiences: string[] = [];
  selectedSkills: string[] = [];

  // Tri
  sortBy: string = 'score';
  sortOrder: 'asc' | 'desc' = 'desc';

  // Affichage des filtres
  showFilters = false;

  // Variables pour la pagination dynamique
  paginationOptions: PaginationOptions = {
    page: 0, // Spring Boot utilise des pages basées sur 0
    size: 10,
    sortBy: 'score',
    sortOrder: 'desc'
  };

  paginatedResponse: PaginatedResponse<TestResultDisplay> | null = null;

  // Indicateur de progression pour l'enrichissement
  enrichmentProgress = 0;
  isEnriching = false;

  // Options de tri avancé
  sortOptions: SortOption[] = [];
  selectedSortOption: SortOption | null = null;

  // Actions candidats
  candidateActions: Map<string, string> = new Map(); // candidateId -> action

  constructor(
    private testResultService: TestResultService,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    this.initializeSortOptions();
    this.testApiConnection();
    this.loadTestResultsPaginated();
    this.loadFilterOptions();
  }

  /**
   * Teste la connexion à l'API backend
   */
  testApiConnection() {
    console.log('🔍 Test de connexion à l\'API depuis le composant...');

    this.testResultService.testApiConnection().subscribe({
      next: (result) => {
        if (result.success) {
          console.log('✅ API backend accessible!');
          this.snackBar.open('✅ Connexion API réussie!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        } else {
          console.error('❌ API backend non accessible:', result.error);
          this.snackBar.open('❌ Erreur de connexion API', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      },
      error: (error) => {
        console.error('❌ Erreur lors du test de connexion:', error);
        this.snackBar.open('❌ Impossible de tester la connexion API', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  /**
   * Initialise les options de tri
   */
  initializeSortOptions() {
    this.sortOptions = this.testResultService.getSortOptions();
    this.selectedSortOption = this.sortOptions[0]; // Score décroissant par défaut
  }

  /**
   * Charge les résultats de test avec pagination
   */
  loadTestResultsPaginated() {
    console.log('🚀 Début du chargement paginé des résultats de test...');
    this.loading = true;
    this.error = null;

    // Synchroniser les options de pagination avec les filtres de tri
    this.paginationOptions.sortBy = this.sortBy;
    this.paginationOptions.sortOrder = this.sortOrder;

    const filters = {
      ...this.filters,
      skills: this.selectedSkills
    };

    this.testResultService.getTestResultsPaginated(this.paginationOptions, filters).subscribe({
      next: (paginatedData) => {
        console.log('📊 Données paginées reçues:', paginatedData);

        // Commencer l'enrichissement des données
        this.isEnriching = true;
        this.enrichmentProgress = 0;

        // Simuler la progression de l'enrichissement
        const progressInterval = setInterval(() => {
          if (this.enrichmentProgress < 90) {
            this.enrichmentProgress += Math.random() * 20;
          }
        }, 200);

        // Transformer les données pour l'affichage avec enrichissement dynamique
        this.testResultService.transformToDisplayFormat(paginatedData.content).subscribe({
          next: (transformedData) => {
            clearInterval(progressInterval);
            this.enrichmentProgress = 100;
            this.isEnriching = false;

            // Créer la réponse paginée avec les données transformées
            this.paginatedResponse = {
              ...paginatedData,
              content: transformedData
            };

            this.results = transformedData;

            console.log('🔄 Données transformées et enrichies:', this.results);
            console.log('📄 Informations de pagination:', {
              currentPage: this.paginatedResponse.currentPage,
              totalPages: this.paginatedResponse.totalPages,
              totalElements: this.paginatedResponse.totalElements,
              pageSize: this.paginatedResponse.pageSize
            });

            this.loading = false;
            console.log('✅ Chargement paginé terminé avec succès!');
          },
          error: (transformError) => {
            clearInterval(progressInterval);
            this.isEnriching = false;
            console.error('❌ Erreur lors de la transformation des données:', transformError);
            this.error = 'Erreur lors du traitement des données. Certaines informations peuvent être manquantes.';
            this.loading = false;
          }
        });
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement paginé:', error);
        this.error = 'Erreur lors du chargement des résultats de test. Vérifiez votre connexion et réessayez.';
        this.loading = false;
      }
    });
  }

  /**
   * Charge les options de filtrage (une seule fois)
   */
  loadFilterOptions() {
    // Charger toutes les données pour extraire les options de filtrage
    this.testResultService.getAllTestResults().subscribe({
      next: (allData) => {
        this.testResultService.transformToDisplayFormat(allData).subscribe({
          next: (transformedData) => {
            this.allResults = transformedData;

            // Initialiser les options de filtrage
            this.locations = this.testResultService.getUniqueLocations(this.allResults);
            this.skills = this.testResultService.getUniqueSkills(this.allResults);
            this.experiences = this.testResultService.getUniqueExperiences(this.allResults);

            console.log('🏷️ Options de filtrage initialisées:', {
              locations: this.locations.length,
              skills: this.skills.length,
              experiences: this.experiences.length
            });
          },
          error: (error) => {
            console.error('❌ Erreur lors du chargement des options de filtrage:', error);
          }
        });
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des données pour les filtres:', error);
      }
    });
  }

  refreshResults() {
    this.loadTestResultsPaginated();
  }

  /**
   * Met à jour les filtres et recharge les données
   */
  onFilterChange() {
    // Remettre à la première page lors d'un changement de filtre
    this.paginationOptions.page = 0;
    this.loadTestResultsPaginated();
  }

  /**
   * Change le tri et recharge les données
   */
  onSortChange(sortBy: string) {
    if (this.sortBy === sortBy) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = sortBy;
      this.sortOrder = 'desc';
    }

    // Remettre à la première page lors d'un changement de tri
    this.paginationOptions.page = 0;
    this.paginationOptions.sortBy = this.sortBy;
    this.paginationOptions.sortOrder = this.sortOrder;

    this.loadTestResultsPaginated();
  }

  /**
   * Toggle l'affichage des filtres
   */
  toggleFilters() {
    this.showFilters = !this.showFilters;
  }

  /**
   * Remet à zéro tous les filtres
   */
  resetFilters() {
    this.filters = {
      location: 'all',
      minScore: 0,
      maxScore: 100,
      skills: [],
      experience: 'all'
    };
    this.selectedSkills = [];
    this.applyFilters();
  }

  /**
   * Ajoute ou retire une compétence des filtres
   */
  toggleSkill(skill: string) {
    const index = this.selectedSkills.indexOf(skill);
    if (index > -1) {
      this.selectedSkills.splice(index, 1);
    } else {
      this.selectedSkills.push(skill);
    }
    this.onFilterChange();
  }

  /**
   * Vérifie si une compétence est sélectionnée
   */
  isSkillSelected(skill: string): boolean {
    return this.selectedSkills.includes(skill);
  }

  /**
   * Navigue vers le profil du candidat
   */
  goToProfile(userId: string) {
    console.log('🔍 Navigation vers le profil du candidat:', userId);

    if (!userId) {
      console.error('❌ UserId manquant pour la navigation');
      alert('Erreur: Impossible de charger le profil du candidat');
      return;
    }

    const candidateName = this.getCandidateName(userId);
    console.log(`👤 Chargement du profil de ${candidateName}...`);

    this.router.navigate(['/profile', userId]).then(
      (success) => {
        if (success) {
          console.log('✅ Navigation réussie vers le profil:', candidateName);
        } else {
          console.error('❌ Échec de la navigation vers le profil:', candidateName);
          alert('Erreur: Impossible d\'accéder au profil du candidat');
        }
      }
    ).catch(error => {
      console.error('❌ Erreur lors de la navigation:', error);
      alert('Erreur technique lors de l\'accès au profil');
    });
  }

  /**
   * Récupère le nom du candidat par son userId
   */
  private getCandidateName(userId: string): string {
    const candidate = this.results.find(result => result.userId === userId);
    return candidate ? candidate.name : 'Candidat inconnu';
  }

  /**
   * Retourne l'icône de tri pour une colonne
   */
  getSortIcon(column: string): string {
    if (this.sortBy !== column) return 'unfold_more';
    return this.sortOrder === 'asc' ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
  }

  /**
   * Gère l'erreur de chargement d'image
   */
  onImageError(event: any) {
    const img = event.target;

    // Éviter la boucle infinie en vérifiant si on a déjà essayé l'image par défaut
    if (img.src.includes('default-avatar')) {
      // Si même l'image par défaut échoue, utiliser une image générée en CSS
      img.style.display = 'none';
      img.parentElement.classList.add('avatar-fallback');
      return;
    }

    // Essayer d'abord le SVG par défaut
    img.src = 'assets/images/default-avatar.svg';

    // Si le SVG échoue aussi, utiliser une image générée
    img.onerror = () => {
      img.style.display = 'none';
      img.parentElement.classList.add('avatar-fallback');
    };
  }

  /**
   * Retourne la couleur du score selon le pourcentage
   */
  getScoreColor(score: string): string {
    const percentage = parseInt(score.replace('%', ''));
    if (percentage >= 80) return '#10B981'; // Vert
    if (percentage >= 60) return '#F59E0B'; // Orange
    if (percentage >= 40) return '#EF4444'; // Rouge
    return '#6B7280'; // Gris
  }

  /**
   * Retourne le statut textuel du score
   */
  getScoreStatus(score: string): string {
    const percentage = parseInt(score.replace('%', ''));
    if (percentage >= 80) return 'Excellent';
    if (percentage >= 60) return 'Bien';
    if (percentage >= 40) return 'Moyen';
    return 'Faible';
  }

  /**
   * Page précédente
   */
  prevPage() {
    if (this.paginatedResponse && this.paginatedResponse.hasPrevious) {
      this.paginationOptions.page--;
      this.loadTestResultsPaginated();
    }
  }

  /**
   * Page suivante
   */
  nextPage() {
    if (this.paginatedResponse && this.paginatedResponse.hasNext) {
      this.paginationOptions.page++;
      this.loadTestResultsPaginated();
    }
  }

  /**
   * Aller à une page spécifique
   */
  goToPage(page: number) {
    this.paginationOptions.page = page;
    this.loadTestResultsPaginated();
  }

  /**
   * Change la taille de page
   */
  changePageSize(size: number) {
    this.paginationOptions.size = size;
    this.paginationOptions.page = 0; // Retour à la première page
    this.loadTestResultsPaginated();
  }

  /**
   * Génère un tableau de numéros de page pour l'affichage
   */
  getPageNumbers(): number[] {
    if (!this.paginatedResponse) return [];

    const totalPages = this.paginatedResponse.totalPages;
    const currentPage = this.paginatedResponse.currentPage;
    const pages: number[] = [];

    // Afficher jusqu'à 5 pages autour de la page actuelle
    const startPage = Math.max(0, currentPage - 2);
    const endPage = Math.min(totalPages - 1, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  /**
   * Vérifie si c'est la page actuelle
   */
  isCurrentPage(page: number): boolean {
    return this.paginatedResponse?.currentPage === page;
  }

  /**
   * Génère une URL d'avatar basée sur le nom
   */
  generateAvatarUrl(name: string): string {
    if (!name) return 'assets/images/default-avatar.svg';

    // Nettoyer le nom pour l'URL
    const cleanName = encodeURIComponent(name.replace(/\s+/g, '+'));

    // Utiliser UI Avatars avec les couleurs de votre charte graphique
    return `https://ui-avatars.com/api/?name=${cleanName}&background=001040&color=fff&size=64&font-size=0.33`;
  }

  /**
   * Change l'option de tri sélectionnée
   */
  onSortOptionChange(sortOption: SortOption) {
    this.selectedSortOption = sortOption;
    this.sortBy = sortOption.field;
    this.sortOrder = sortOption.direction;

    // Remettre à la première page et recharger
    this.paginationOptions.page = 0;
    this.paginationOptions.sortBy = this.sortBy;
    this.paginationOptions.sortOrder = this.sortOrder;

    this.loadTestResultsPaginated();
  }

  /**
   * Accepte un candidat et ouvre le dialog de planification d'entretien
   */
  acceptCandidate(candidate: TestResultDisplay) {
    console.log('✅ Acceptation du candidat:', candidate.name);

    // Créer les données pour l'entretien
    const interviewRequest: InterviewRequest = {
      candidateId: candidate.userId,
      candidateName: candidate.name,
      candidateEmail: this.getCandidateEmail(candidate.userId),
      testResultId: candidate.id,
      interviewDate: this.getNextBusinessDay(),
      interviewTime: '14:00',
      location: 'Bureaux de l\'entreprise - 123 Rue de la Tech, 75001 Paris',
      interviewType: 'presentiel',
      message: `Félicitations ! Vos résultats au test ${candidate.job} (${candidate.score}) sont excellents.`,
      recruiterName: 'Sarah Martin',
      recruiterEmail: '<EMAIL>',
      companyName: 'TechCorp'
    };

    // Créer l'action candidat
    const candidateAction: CandidateAction = {
      candidateId: candidate.userId,
      candidateName: candidate.name,
      candidateEmail: this.getCandidateEmail(candidate.userId),
      action: 'accept',
      testResultId: candidate.id,
      score: candidate.scorePercentage,
      testName: candidate.job
    };

    // Envoyer l'acceptation et l'email
    this.testResultService.acceptCandidate(candidateAction, interviewRequest).subscribe({
      next: (response) => {
        console.log('✅ Candidat accepté et email envoyé:', response);

        // Mettre à jour l'état local
        this.candidateActions.set(candidate.userId, 'accepted');

        // Afficher un message de succès
        this.snackBar.open(
          `✅ ${candidate.name} a été accepté(e) et un email d'invitation a été envoyé !`,
          'Fermer',
          {
            duration: 5000,
            panelClass: ['success-snackbar']
          }
        );

        // Recharger les données
        this.loadTestResultsPaginated();
      },
      error: (error) => {
        console.error('❌ Erreur lors de l\'acceptation:', error);
        this.snackBar.open(
          `❌ Erreur lors de l'acceptation de ${candidate.name}`,
          'Fermer',
          {
            duration: 5000,
            panelClass: ['error-snackbar']
          }
        );
      }
    });
  }

  /**
   * Rejette un candidat
   */
  rejectCandidate(candidate: TestResultDisplay) {
    console.log('❌ Rejet du candidat:', candidate.name);

    const candidateAction: CandidateAction = {
      candidateId: candidate.userId,
      candidateName: candidate.name,
      candidateEmail: this.getCandidateEmail(candidate.userId),
      action: 'reject',
      testResultId: candidate.id,
      score: candidate.scorePercentage,
      testName: candidate.job
    };

    this.testResultService.rejectCandidate(candidateAction).subscribe({
      next: (response) => {
        console.log('✅ Candidat rejeté:', response);

        // Mettre à jour l'état local
        this.candidateActions.set(candidate.userId, 'rejected');

        // Afficher un message de succès
        this.snackBar.open(
          `❌ ${candidate.name} a été rejeté(e)`,
          'Fermer',
          {
            duration: 3000,
            panelClass: ['info-snackbar']
          }
        );

        // Recharger les données
        this.loadTestResultsPaginated();
      },
      error: (error) => {
        console.error('❌ Erreur lors du rejet:', error);
        this.snackBar.open(
          `❌ Erreur lors du rejet de ${candidate.name}`,
          'Fermer',
          {
            duration: 5000,
            panelClass: ['error-snackbar']
          }
        );
      }
    });
  }

  /**
   * Récupère l'email du candidat (à implémenter selon votre logique)
   */
  private getCandidateEmail(userId: string): string {
    // Pour l'instant, générer un email basé sur le nom
    const candidate = this.results.find(r => r.userId === userId);
    if (candidate) {
      const cleanName = candidate.name.toLowerCase()
        .replace(/\s+/g, '.')
        .replace(/[^a-z.]/g, '');
      return `${cleanName}@email.com`;
    }
    return `user${userId}@email.com`;
  }

  /**
   * Retourne le prochain jour ouvrable
   */
  private getNextBusinessDay(): Date {
    const date = new Date();
    date.setDate(date.getDate() + 1); // Demain

    // Si c'est un weekend, aller au lundi suivant
    while (date.getDay() === 0 || date.getDay() === 6) {
      date.setDate(date.getDate() + 1);
    }

    return date;
  }

  /**
   * Retourne l'action actuelle pour un candidat
   */
  getCandidateAction(candidateId: string): string {
    return this.candidateActions.get(candidateId) || 'pending';
  }

  /**
   * Vérifie si un candidat a été accepté
   */
  isCandidateAccepted(candidateId: string): boolean {
    return this.getCandidateAction(candidateId) === 'accepted';
  }

  /**
   * Vérifie si un candidat a été rejeté
   */
  isCandidateRejected(candidateId: string): boolean {
    return this.getCandidateAction(candidateId) === 'rejected';
  }

  /**
   * Applique les filtres (méthode manquante)
   */
  applyFilters() {
    // Cette méthode était référencée mais manquante
    this.onFilterChange();
  }

  /**
   * Teste la récupération des données sans authentification
   */
  testWithoutAuth() {
    console.log('🔍 Test de récupération sans authentification...');

    this.testResultService.getTestResultsWithoutAuth().subscribe({
      next: (result) => {
        console.log('📊 Résultat du test sans auth:', result);
        this.snackBar.open('✅ Test sans auth terminé - Voir console', 'Fermer', {
          duration: 3000,
          panelClass: ['info-snackbar']
        });
      },
      error: (error) => {
        console.error('❌ Erreur test sans auth:', error);
        this.snackBar.open('❌ Erreur test sans auth', 'Fermer', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }
}
